"""
Metrics collection utilities
"""

import time
from typing import Dict, Optional, List, Any
from collections import defaultdict, deque
from dataclasses import dataclass, field
from threading import Lock
import structlog

from ...core.domain.services import MetricsCollector

logger = structlog.get_logger(__name__)


@dataclass
class MetricValue:
    """Single metric value with timestamp"""
    value: float
    timestamp: float
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class HistogramStats:
    """Histogram statistics"""
    count: int = 0
    sum: float = 0.0
    min: float = float('inf')
    max: float = float('-inf')
    values: deque = field(default_factory=lambda: deque(maxlen=1000))
    
    def add_value(self, value: float) -> None:
        """Add a value to histogram"""
        self.count += 1
        self.sum += value
        self.min = min(self.min, value)
        self.max = max(self.max, value)
        self.values.append(value)
    
    @property
    def avg(self) -> float:
        """Average value"""
        return self.sum / self.count if self.count > 0 else 0.0
    
    def percentile(self, p: float) -> float:
        """Calculate percentile"""
        if not self.values:
            return 0.0
        
        sorted_values = sorted(self.values)
        index = int(len(sorted_values) * p / 100)
        return sorted_values[min(index, len(sorted_values) - 1)]


class MetricsCollectorImpl(MetricsCollector):
    """
    In-memory metrics collector implementation
    """
    
    def __init__(self, max_history: int = 10000):
        self.max_history = max_history
        self._lock = Lock()
        
        # Metric storage
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = {}
        self._histograms: Dict[str, HistogramStats] = defaultdict(HistogramStats)
        
        # Tagged metrics
        self._tagged_counters: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self._tagged_gauges: Dict[str, Dict[str, float]] = defaultdict(dict)
        self._tagged_histograms: Dict[str, Dict[str, HistogramStats]] = defaultdict(lambda: defaultdict(HistogramStats))
        
        logger.info("Metrics collector initialized", max_history=max_history)
    
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric"""
        with self._lock:
            if tags:
                tag_key = self._serialize_tags(tags)
                self._tagged_counters[name][tag_key] += 1
            else:
                self._counters[name] += 1
    
    def record_histogram(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a histogram value"""
        with self._lock:
            if tags:
                tag_key = self._serialize_tags(tags)
                self._tagged_histograms[name][tag_key].add_value(value)
            else:
                self._histograms[name].add_value(value)
    
    def set_gauge(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Set a gauge value"""
        with self._lock:
            if tags:
                tag_key = self._serialize_tags(tags)
                self._tagged_gauges[name][tag_key] = value
            else:
                self._gauges[name] = value
    
    def get_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> int:
        """Get counter value"""
        with self._lock:
            if tags:
                tag_key = self._serialize_tags(tags)
                return self._tagged_counters[name].get(tag_key, 0)
            else:
                return self._counters.get(name, 0)
    
    def get_gauge(self, name: str, tags: Optional[Dict[str, str]] = None) -> float:
        """Get gauge value"""
        with self._lock:
            if tags:
                tag_key = self._serialize_tags(tags)
                return self._tagged_gauges[name].get(tag_key, 0.0)
            else:
                return self._gauges.get(name, 0.0)
    
    def get_histogram_stats(self, name: str, tags: Optional[Dict[str, str]] = None) -> Dict[str, float]:
        """Get histogram statistics"""
        with self._lock:
            if tags:
                tag_key = self._serialize_tags(tags)
                hist = self._tagged_histograms[name].get(tag_key, HistogramStats())
            else:
                hist = self._histograms.get(name, HistogramStats())
            
            return {
                'count': hist.count,
                'sum': hist.sum,
                'avg': hist.avg,
                'min': hist.min if hist.count > 0 else 0.0,
                'max': hist.max if hist.count > 0 else 0.0,
                'p50': hist.percentile(50),
                'p95': hist.percentile(95),
                'p99': hist.percentile(99)
            }
    
    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all metrics"""
        with self._lock:
            return {
                'counters': dict(self._counters),
                'gauges': dict(self._gauges),
                'histograms': {
                    name: self.get_histogram_stats(name) 
                    for name in self._histograms.keys()
                },
                'tagged_counters': {
                    name: dict(counters) 
                    for name, counters in self._tagged_counters.items()
                },
                'tagged_gauges': {
                    name: dict(gauges) 
                    for name, gauges in self._tagged_gauges.items()
                },
                'tagged_histograms': {
                    name: {
                        tag_key: self.get_histogram_stats(name, self._deserialize_tags(tag_key))
                        for tag_key in histograms.keys()
                    }
                    for name, histograms in self._tagged_histograms.items()
                }
            }
    
    def reset_metrics(self) -> None:
        """Reset all metrics"""
        with self._lock:
            self._counters.clear()
            self._gauges.clear()
            self._histograms.clear()
            self._tagged_counters.clear()
            self._tagged_gauges.clear()
            self._tagged_histograms.clear()
        
        logger.info("All metrics reset")
    
    def _serialize_tags(self, tags: Dict[str, str]) -> str:
        """Serialize tags to string key"""
        return ','.join(f"{k}={v}" for k, v in sorted(tags.items()))
    
    def _deserialize_tags(self, tag_key: str) -> Dict[str, str]:
        """Deserialize tags from string key"""
        if not tag_key:
            return {}
        
        tags = {}
        for pair in tag_key.split(','):
            if '=' in pair:
                k, v = pair.split('=', 1)
                tags[k] = v
        return tags


class PerformanceTimer:
    """
    Context manager for timing operations
    """
    
    def __init__(
        self, 
        metrics_collector: MetricsCollector, 
        metric_name: str,
        tags: Optional[Dict[str, str]] = None
    ):
        self.metrics_collector = metrics_collector
        self.metric_name = metric_name
        self.tags = tags
        self.start_time: Optional[float] = None
    
    def __enter__(self) -> 'PerformanceTimer':
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        if self.start_time is not None:
            duration_ms = (time.time() - self.start_time) * 1000
            self.metrics_collector.record_histogram(
                self.metric_name, 
                duration_ms, 
                self.tags
            )


class MetricsReporter:
    """
    Periodic metrics reporter
    """
    
    def __init__(self, metrics_collector: MetricsCollectorImpl):
        self.metrics_collector = metrics_collector
        self.last_report_time = time.time()
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate metrics report"""
        current_time = time.time()
        time_since_last = current_time - self.last_report_time
        
        all_metrics = self.metrics_collector.get_all_metrics()
        
        # Add metadata
        report = {
            'timestamp': current_time,
            'time_since_last_report': time_since_last,
            'metrics': all_metrics
        }
        
        self.last_report_time = current_time
        return report
    
    def log_summary(self) -> None:
        """Log metrics summary"""
        report = self.generate_report()
        metrics = report['metrics']
        
        # Log key metrics
        logger.info(
            "Metrics summary",
            counters_count=len(metrics['counters']),
            gauges_count=len(metrics['gauges']),
            histograms_count=len(metrics['histograms']),
            time_since_last=report['time_since_last_report']
        )
        
        # Log top counters
        if metrics['counters']:
            top_counters = sorted(
                metrics['counters'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
            
            logger.info(
                "Top counters",
                counters=dict(top_counters)
            )
        
        # Log histogram summaries
        for name, stats in metrics['histograms'].items():
            if stats['count'] > 0:
                logger.info(
                    "Histogram stats",
                    name=name,
                    count=stats['count'],
                    avg=round(stats['avg'], 2),
                    p95=round(stats['p95'], 2),
                    p99=round(stats['p99'], 2)
                )
