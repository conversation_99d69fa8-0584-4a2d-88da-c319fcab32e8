"""
Validation utilities for input validation
"""

import re
from typing import Optional, List, Dict, Any
from core.domain.exceptions import ValidationError, InvalidQueryError
from shared.types import QueryText, Confidence


def validate_query(query: QueryText) -> None:
    """
    Validate query input
    
    Args:
        query: Query text to validate
        
    Raises:
        InvalidQueryError: If query is invalid
    """
    if not query:
        raise InvalidQueryError(query, "Query cannot be empty")
    
    if not isinstance(query, str):
        raise InvalidQueryError(str(query), "Query must be a string")
    
    # Check length limits
    if len(query) > 1000:
        raise InvalidQueryError(query, "Query too long (max 1000 characters)")
    
    if len(query.strip()) < 2:
        raise InvalidQueryError(query, "Query too short (min 2 characters)")
    
    # Check for suspicious patterns
    if re.search(r'[<>{}[\]\\]', query):
        raise InvalidQueryError(query, "Query contains invalid characters")


def validate_confidence(confidence: Confidence) -> None:
    """
    Validate confidence score
    
    Args:
        confidence: Confidence score to validate
        
    Raises:
        ValidationError: If confidence is invalid
    """
    if not isinstance(confidence, (int, float)):
        raise ValidationError(
            "confidence", 
            confidence, 
            "Confidence must be a number"
        )
    
    if not 0.0 <= confidence <= 1.0:
        raise ValidationError(
            "confidence",
            confidence,
            "Confidence must be between 0.0 and 1.0"
        )


def validate_intent_id(intent_id: str) -> None:
    """
    Validate intent ID format
    
    Args:
        intent_id: Intent ID to validate
        
    Raises:
        ValidationError: If intent ID is invalid
    """
    if not intent_id:
        raise ValidationError("intent_id", intent_id, "Intent ID cannot be empty")
    
    if not isinstance(intent_id, str):
        raise ValidationError("intent_id", intent_id, "Intent ID must be a string")
    
    # Check format (lowercase with underscores)
    if not re.match(r'^[a-z][a-z0-9_]*$', intent_id):
        raise ValidationError(
            "intent_id",
            intent_id,
            "Intent ID must start with lowercase letter and contain only lowercase letters, numbers, and underscores"
        )
    
    if len(intent_id) > 50:
        raise ValidationError("intent_id", intent_id, "Intent ID too long (max 50 characters)")


def validate_metadata(metadata: Dict[str, Any]) -> None:
    """
    Validate metadata dictionary
    
    Args:
        metadata: Metadata to validate
        
    Raises:
        ValidationError: If metadata is invalid
    """
    if not isinstance(metadata, dict):
        raise ValidationError("metadata", type(metadata), "Metadata must be a dictionary")
    
    # Check for reserved keys
    reserved_keys = {'_id', '_type', '_internal'}
    for key in metadata.keys():
        if key in reserved_keys:
            raise ValidationError("metadata", key, f"Key '{key}' is reserved")
        
        if not isinstance(key, str):
            raise ValidationError("metadata", key, "Metadata keys must be strings")
        
        if len(key) > 100:
            raise ValidationError("metadata", key, "Metadata key too long (max 100 characters)")


def validate_pagination_params(page: int, limit: int) -> None:
    """
    Validate pagination parameters
    
    Args:
        page: Page number
        limit: Items per page
        
    Raises:
        ValidationError: If parameters are invalid
    """
    if not isinstance(page, int) or page < 1:
        raise ValidationError("page", page, "Page must be a positive integer")
    
    if not isinstance(limit, int) or limit < 1:
        raise ValidationError("limit", limit, "Limit must be a positive integer")
    
    if limit > 100:
        raise ValidationError("limit", limit, "Limit too large (max 100)")


def validate_threshold_config(thresholds: Dict[str, float]) -> None:
    """
    Validate threshold configuration
    
    Args:
        thresholds: Threshold configuration
        
    Raises:
        ValidationError: If thresholds are invalid
    """
    required_keys = {'very_high', 'high', 'medium', 'low'}
    
    for key in required_keys:
        if key not in thresholds:
            raise ValidationError("thresholds", key, f"Missing required threshold '{key}'")
        
        value = thresholds[key]
        if not isinstance(value, (int, float)):
            raise ValidationError("thresholds", value, f"Threshold '{key}' must be a number")
        
        if not 0.0 <= value <= 1.0:
            raise ValidationError("thresholds", value, f"Threshold '{key}' must be between 0.0 and 1.0")
    
    # Check ordering (should be descending)
    values = [thresholds[key] for key in ['very_high', 'high', 'medium', 'low']]
    if not all(values[i] >= values[i+1] for i in range(len(values)-1)):
        raise ValidationError("thresholds", values, "Thresholds must be in descending order")


def validate_rule_patterns(patterns: List[str]) -> None:
    """
    Validate regex patterns
    
    Args:
        patterns: List of regex patterns
        
    Raises:
        ValidationError: If patterns are invalid
    """
    if not isinstance(patterns, list):
        raise ValidationError("patterns", type(patterns), "Patterns must be a list")
    
    for i, pattern in enumerate(patterns):
        if not isinstance(pattern, str):
            raise ValidationError("patterns", pattern, f"Pattern {i} must be a string")
        
        try:
            re.compile(pattern)
        except re.error as e:
            raise ValidationError("patterns", pattern, f"Invalid regex pattern {i}: {e}")


def validate_keywords(keywords: List[str]) -> None:
    """
    Validate keyword list
    
    Args:
        keywords: List of keywords
        
    Raises:
        ValidationError: If keywords are invalid
    """
    if not isinstance(keywords, list):
        raise ValidationError("keywords", type(keywords), "Keywords must be a list")
    
    for i, keyword in enumerate(keywords):
        if not isinstance(keyword, str):
            raise ValidationError("keywords", keyword, f"Keyword {i} must be a string")
        
        if not keyword.strip():
            raise ValidationError("keywords", keyword, f"Keyword {i} cannot be empty")
        
        if len(keyword) > 100:
            raise ValidationError("keywords", keyword, f"Keyword {i} too long (max 100 characters)")


def validate_weight(weight: float) -> None:
    """
    Validate rule weight
    
    Args:
        weight: Rule weight
        
    Raises:
        ValidationError: If weight is invalid
    """
    if not isinstance(weight, (int, float)):
        raise ValidationError("weight", weight, "Weight must be a number")
    
    if weight <= 0:
        raise ValidationError("weight", weight, "Weight must be positive")
    
    if weight > 10:
        raise ValidationError("weight", weight, "Weight too large (max 10)")


def sanitize_query(query: str) -> str:
    """
    Sanitize query by removing potentially harmful content
    
    Args:
        query: Raw query
        
    Returns:
        Sanitized query
    """
    if not query:
        return ""
    
    # Remove HTML tags
    query = re.sub(r'<[^>]+>', '', query)
    
    # Remove script content
    query = re.sub(r'<script.*?</script>', '', query, flags=re.DOTALL | re.IGNORECASE)
    
    # Remove potentially harmful characters
    query = re.sub(r'[<>{}[\]\\]', '', query)
    
    # Normalize whitespace
    query = re.sub(r'\s+', ' ', query.strip())
    
    return query
