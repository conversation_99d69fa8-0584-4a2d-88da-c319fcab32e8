"""
Text processing utilities for Vietnamese language support
"""

import re
from typing import List, Dict, Optional
import structlog

try:
    from unidecode import unidecode
    UNIDECODE_AVAILABLE = True
except ImportError:
    UNIDECODE_AVAILABLE = False

logger = structlog.get_logger(__name__)


class VietnameseTextProcessor:
    """
    Vietnamese text processing utilities with normalization and keyword extraction
    """
    
    def __init__(self):
        self.abbreviation_map = {
            'fpt': 'fpt university',
            'cntt': 'công nghệ thông tin',
            'it': 'information technology',
            'ai': 'artificial intelligence',
            'se': 'software engineering',
            'bkhn': 'bach khoa ha noi',
            'hcm': 'ho chi minh',
            'hn': 'ha noi',
            'ojt': 'on job training'
        }
        
        self.technical_tuition_map = {
            'se program tuition': 'software engineering học phí',
            'it program tuition': 'information technology học phí',
            'ai program tuition': 'artificial intelligence học phí',
            'program tuition': 'chương trình học phí',
            'tuition fee': 'học phí',
            'course fee': 'học phí khóa học',
            'program fee': 'học phí chương trình'
        }
        
        self.vietnamese_map = {
            'hoc phi': 'học phí',
            'nganh': 'ngành',
            'an toan thong tin': 'an toàn thông tin',
            'han chot': 'hạn chót',
            'nop ho so': 'nộp hồ sơ',
            'diem chuan': 'điểm chuẩn',
            'hoc bong': 'học bổng',
            'thu vien': 'thư viện',
            'ky tuc xa': 'ký túc xá',
            'so sanh': 'so sánh',
            'tuong lai': 'tương lai',
            'viec lam': 'việc làm'
        }
        
        # Vietnamese diacritics pattern
        self.vietnamese_pattern = re.compile(
            r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]'
        )
        
        logger.debug("Vietnamese text processor initialized")
    
    def normalize_vietnamese(self, text: str) -> str:
        """
        Comprehensive Vietnamese text normalization
        
        Args:
            text: Input text to normalize
            
        Returns:
            Normalized text
        """
        if not text:
            return text
        
        # Step 1: Basic cleaning
        text = text.lower().strip()
        
        # Step 2: Apply technical tuition mapping first (higher priority)
        for tech_term, replacement in self.technical_tuition_map.items():
            text = text.replace(tech_term, replacement)
        
        # Step 3: Apply abbreviation mapping
        for abbr, full in self.abbreviation_map.items():
            text = text.replace(abbr, full)
        
        # Step 4: Apply Vietnamese accent mapping
        for no_accent, with_accent in self.vietnamese_map.items():
            text = text.replace(no_accent, with_accent)
        
        # Step 5: Use unidecode if available for additional normalization
        if UNIDECODE_AVAILABLE:
            # Create accent-free version for better matching
            text_no_accent = unidecode(text)
            # For now, return original with mappings applied
            # Could implement more sophisticated accent-aware matching
            return text
        
        return text
    
    def clean_text(self, text: str) -> str:
        """
        Clean text by removing extra whitespace and special characters
        
        Args:
            text: Input text to clean
            
        Returns:
            Cleaned text
        """
        if not text:
            return text
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special characters but keep Vietnamese diacritics
        text = re.sub(r'[^\w\sàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', ' ', text)
        
        # Remove extra whitespace again
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def extract_keywords(self, text: str) -> List[str]:
        """
        Extract meaningful keywords from text
        
        Args:
            text: Input text
            
        Returns:
            List of extracted keywords
        """
        if not text:
            return []
        
        # Normalize and clean text first
        normalized_text = self.normalize_vietnamese(text)
        cleaned_text = self.clean_text(normalized_text)
        
        # Split into words
        words = cleaned_text.lower().split()
        
        # Filter out common stop words (Vietnamese and English)
        stop_words = {
            # Vietnamese stop words
            'và', 'của', 'có', 'là', 'được', 'trong', 'với', 'để', 'cho', 'từ', 'về',
            'như', 'khi', 'nếu', 'mà', 'hay', 'hoặc', 'nhưng', 'vì', 'do', 'theo',
            'trên', 'dưới', 'giữa', 'bên', 'cạnh', 'gần', 'xa', 'này', 'đó', 'kia',
            'tôi', 'bạn', 'anh', 'chị', 'em', 'chúng', 'họ', 'mình', 'ta',
            # English stop words
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        # Filter keywords
        keywords = [word for word in words if len(word) > 2 and word not in stop_words]
        
        return keywords
    
    def detect_language(self, text: str) -> str:
        """
        Detect primary language of text
        
        Args:
            text: Input text
            
        Returns:
            Language code ('vi', 'en', or 'mixed')
        """
        if not text:
            return 'unknown'
        
        # Check for Vietnamese diacritics
        has_vietnamese = bool(self.vietnamese_pattern.search(text))
        
        # Check for English characters
        has_english = bool(re.search(r'[a-zA-Z]', text))
        
        if has_vietnamese and not has_english:
            return 'vi'
        elif has_english and not has_vietnamese:
            return 'en'
        elif has_vietnamese and has_english:
            return 'mixed'
        else:
            return 'unknown'
    
    def is_irrelevant_query(self, text: str) -> bool:
        """
        Check if query is irrelevant to university domain
        
        Args:
            text: Query text to check
            
        Returns:
            True if query is irrelevant
        """
        irrelevant_keywords = [
            # Weather
            'thời tiết', 'weather', 'mưa', 'nắng', 'trời', 'rain', 'sun',
            # Food
            'nấu ăn', 'cooking', 'món ăn', 'recipe', 'phở', 'cơm',
            # Finance
            'giá vàng', 'gold price', 'chứng khoán', 'stock', 'bitcoin',
            # Sports
            'bóng đá', 'football', 'world cup', 'thể thao', 'sport',
            # Entertainment
            'âm nhạc', 'music', 'ca sĩ', 'singer', 'bài hát', 'song',
            'phim', 'movie', 'diễn viên', 'actor'
        ]
        
        text_lower = text.lower()
        for keyword in irrelevant_keywords:
            if keyword in text_lower:
                logger.debug(
                    "Irrelevant query detected",
                    query=text,
                    matched_keyword=keyword
                )
                return True
        
        return False
    
    def get_text_statistics(self, text: str) -> Dict[str, int]:
        """
        Get basic statistics about text
        
        Args:
            text: Input text
            
        Returns:
            Dictionary with text statistics
        """
        if not text:
            return {
                'character_count': 0,
                'word_count': 0,
                'sentence_count': 0,
                'vietnamese_chars': 0,
                'english_chars': 0
            }
        
        # Count characters
        char_count = len(text)
        
        # Count words
        words = text.split()
        word_count = len(words)
        
        # Count sentences (rough estimate)
        sentence_count = len(re.findall(r'[.!?]+', text))
        
        # Count Vietnamese and English characters
        vietnamese_chars = len(self.vietnamese_pattern.findall(text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        
        return {
            'character_count': char_count,
            'word_count': word_count,
            'sentence_count': max(sentence_count, 1),  # At least 1 sentence
            'vietnamese_chars': vietnamese_chars,
            'english_chars': english_chars
        }
