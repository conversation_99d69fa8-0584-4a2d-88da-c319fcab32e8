"""
Domain service interfaces (abstract base classes and protocols)
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, AsyncIterator, Protocol
from contextlib import asynccontextmanager

from .entities import (
    IntentResult, SearchCandidate, RuleMatch, DetectionContext,
    IntentRule, DetectionSession
)
from ...shared.types import (
    QueryText, IntentId, Confidence, Score, Metadata, CacheKey,
    Result, PaginationParams, PaginatedResult
)


class IntentDetectionService(ABC):
    """
    Abstract base class for intent detection services
    """
    
    @abstractmethod
    async def detect(self, context: DetectionContext) -> Result[IntentResult]:
        """
        Detect intent from query context
        
        Args:
            context: Detection context with query and metadata
            
        Returns:
            Result containing IntentResult or error
        """
        pass
    
    @abstractmethod
    async def detect_batch(
        self, 
        contexts: List[DetectionContext]
    ) -> List[Result[IntentResult]]:
        """
        Detect intents for multiple queries in batch
        
        Args:
            contexts: List of detection contexts
            
        Returns:
            List of results for each context
        """
        pass
    
    @abstractmethod
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for monitoring"""
        pass
    
    @abstractmethod
    async def health_check(self) -> Dict[str, Any]:
        """Check service health status"""
        pass


class RuleBasedDetector(ABC):
    """
    Abstract base class for rule-based intent detection
    """
    
    @abstractmethod
    async def detect(self, query: QueryText) -> Optional[RuleMatch]:
        """
        Detect intent using rule-based matching
        
        Args:
            query: Input query text
            
        Returns:
            RuleMatch if found, None otherwise
        """
        pass
    
    @abstractmethod
    async def get_rules(self) -> List[IntentRule]:
        """Get all active rules"""
        pass
    
    @abstractmethod
    async def add_rule(self, rule: IntentRule) -> None:
        """Add a new rule"""
        pass
    
    @abstractmethod
    async def update_rule(self, rule: IntentRule) -> None:
        """Update existing rule"""
        pass
    
    @abstractmethod
    async def delete_rule(self, intent_id: IntentId) -> None:
        """Delete rule by intent ID"""
        pass


class VectorStore(ABC):
    """
    Abstract base class for vector storage operations
    """
    
    @abstractmethod
    async def search(
        self, 
        query_vector: List[float], 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchCandidate]:
        """
        Search for similar vectors
        
        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filters: Optional metadata filters
            
        Returns:
            List of search candidates
        """
        pass
    
    @abstractmethod
    async def add_documents(
        self, 
        texts: List[str], 
        vectors: List[List[float]],
        metadata: List[Metadata]
    ) -> None:
        """
        Add documents to vector store
        
        Args:
            texts: Document texts
            vectors: Embedding vectors
            metadata: Document metadata
        """
        pass
    
    @abstractmethod
    async def delete_documents(self, document_ids: List[str]) -> None:
        """Delete documents by IDs"""
        pass
    
    @abstractmethod
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get collection information and statistics"""
        pass


class EmbeddingProvider(ABC):
    """
    Abstract base class for embedding generation
    """
    
    @abstractmethod
    async def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for single text
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        pass
    
    @abstractmethod
    async def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts
        
        Args:
            texts: List of input texts
            
        Returns:
            List of embedding vectors
        """
        pass
    
    @abstractmethod
    def get_embedding_dimension(self) -> int:
        """Get embedding vector dimension"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get embedding model information"""
        pass


class RerankerService(ABC):
    """
    Abstract base class for reranking services
    """
    
    @abstractmethod
    async def rerank(
        self, 
        query: QueryText, 
        candidates: List[SearchCandidate]
    ) -> List[SearchCandidate]:
        """
        Rerank search candidates
        
        Args:
            query: Original query
            candidates: Search candidates to rerank
            
        Returns:
            Reranked candidates with updated scores
        """
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get reranker model information"""
        pass


class CacheService(ABC):
    """
    Abstract base class for caching services
    """
    
    @abstractmethod
    async def get(self, key: CacheKey) -> Optional[Any]:
        """Get value from cache"""
        pass
    
    @abstractmethod
    async def set(
        self, 
        key: CacheKey, 
        value: Any, 
        ttl_seconds: Optional[int] = None
    ) -> None:
        """Set value in cache with optional TTL"""
        pass
    
    @abstractmethod
    async def delete(self, key: CacheKey) -> None:
        """Delete key from cache"""
        pass
    
    @abstractmethod
    async def clear(self) -> None:
        """Clear all cache entries"""
        pass
    
    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        pass


class TextProcessor(Protocol):
    """
    Protocol for text processing utilities
    """
    
    def normalize_vietnamese(self, text: str) -> str:
        """Normalize Vietnamese text"""
        ...
    
    def clean_text(self, text: str) -> str:
        """Clean and preprocess text"""
        ...
    
    def extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        ...
    
    def detect_language(self, text: str) -> str:
        """Detect text language"""
        ...


class MetricsCollector(Protocol):
    """
    Protocol for metrics collection
    """
    
    def increment_counter(self, name: str, tags: Optional[Dict[str, str]] = None) -> None:
        """Increment a counter metric"""
        ...
    
    def record_histogram(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Record a histogram value"""
        ...
    
    def set_gauge(
        self, 
        name: str, 
        value: float, 
        tags: Optional[Dict[str, str]] = None
    ) -> None:
        """Set a gauge value"""
        ...


class SessionManager(ABC):
    """
    Abstract base class for session management
    """

    @abstractmethod
    async def create_session(
        self,
        user_id: Optional[str] = None
    ) -> DetectionSession:
        """Create a new detection session"""
        pass

    @abstractmethod
    async def get_session(self, session_id: str) -> Optional[DetectionSession]:
        """Get session by ID"""
        pass

    @abstractmethod
    async def update_session(self, session: DetectionSession) -> None:
        """Update session data"""
        pass

    @abstractmethod
    async def close_session(self, session_id: str) -> None:
        """Close and finalize session"""
        pass

    @abstractmethod
    async def get_user_sessions(
        self,
        user_id: str,
        pagination: PaginationParams
    ) -> PaginatedResult[DetectionSession]:
        """Get sessions for a user with pagination"""
        pass


# Context managers for resource management
@asynccontextmanager
async def managed_vector_store(store: VectorStore) -> AsyncIterator[VectorStore]:
    """Context manager for vector store with proper cleanup"""
    try:
        yield store
    finally:
        # Cleanup logic if needed
        pass


@asynccontextmanager
async def managed_cache(cache: CacheService) -> AsyncIterator[CacheService]:
    """Context manager for cache service with proper cleanup"""
    try:
        yield cache
    finally:
        # Cleanup logic if needed
        pass
