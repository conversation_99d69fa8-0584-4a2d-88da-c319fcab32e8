"""
Value objects for the intent detection domain
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
import re

from ...shared.types import QueryText, IntentId, Confidence, Score


@dataclass(frozen=True)
class QueryAnalysis:
    """
    Value object representing analysis of a query
    """
    original_query: QueryText
    normalized_query: QueryText
    language: str
    word_count: int
    character_count: int
    contains_vietnamese: bool
    contains_english: bool
    is_mixed_language: bool
    detected_keywords: List[str]
    
    @classmethod
    def analyze(cls, query: QueryText) -> 'QueryAnalysis':
        """Create QueryAnalysis from raw query"""
        normalized = query.lower().strip()
        words = normalized.split()
        
        # Simple language detection
        vietnamese_chars = bool(re.search(r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', normalized))
        english_chars = bool(re.search(r'[a-zA-Z]', normalized))
        
        return cls(
            original_query=query,
            normalized_query=normalized,
            language="vi" if vietnamese_chars and not english_chars else "en" if english_chars and not vietnamese_chars else "mixed",
            word_count=len(words),
            character_count=len(query),
            contains_vietnamese=vietnamese_chars,
            contains_english=english_chars,
            is_mixed_language=vietnamese_chars and english_chars,
            detected_keywords=words  # Simplified keyword extraction
        )


@dataclass(frozen=True)
class ConfidenceScore:
    """
    Value object representing a confidence score with metadata
    """
    value: Confidence
    source: str
    reasoning: Optional[str] = None
    
    def __post_init__(self):
        if not 0.0 <= self.value <= 1.0:
            raise ValueError(f"Confidence must be between 0.0 and 1.0, got {self.value}")
    
    @classmethod
    def from_rule_match(cls, score: float, matched_items: List[str]) -> 'ConfidenceScore':
        """Create confidence score from rule matching"""
        reasoning = f"Matched {len(matched_items)} items: {', '.join(matched_items[:3])}"
        if len(matched_items) > 3:
            reasoning += f" and {len(matched_items) - 3} more"
        
        return cls(
            value=min(score, 1.0),
            source="rule_based",
            reasoning=reasoning
        )
    
    @classmethod
    def from_vector_similarity(cls, similarity: float, threshold: float) -> 'ConfidenceScore':
        """Create confidence score from vector similarity"""
        return cls(
            value=min(similarity, 1.0),
            source="vector_similarity",
            reasoning=f"Similarity {similarity:.3f} vs threshold {threshold:.3f}"
        )
    
    @classmethod
    def from_reranker(cls, score: float, rank: int, total: int) -> 'ConfidenceScore':
        """Create confidence score from reranker"""
        return cls(
            value=min(max(score, 0.0), 1.0),  # Normalize reranker scores
            source="reranker",
            reasoning=f"Ranked {rank}/{total} with score {score:.3f}"
        )


@dataclass(frozen=True)
class ThresholdConfig:
    """
    Value object for confidence thresholds
    """
    very_high: float = 0.9
    high: float = 0.7
    medium: float = 0.5
    low: float = 0.3
    
    def __post_init__(self):
        thresholds = [self.very_high, self.high, self.medium, self.low]
        if not all(0.0 <= t <= 1.0 for t in thresholds):
            raise ValueError("All thresholds must be between 0.0 and 1.0")
        if not all(thresholds[i] >= thresholds[i+1] for i in range(len(thresholds)-1)):
            raise ValueError("Thresholds must be in descending order")
    
    def get_level(self, confidence: float) -> str:
        """Get confidence level for a given confidence score"""
        if confidence >= self.very_high:
            return "very_high"
        elif confidence >= self.high:
            return "high"
        elif confidence >= self.medium:
            return "medium"
        elif confidence >= self.low:
            return "low"
        else:
            return "very_low"


@dataclass(frozen=True)
class ProcessingMetrics:
    """
    Value object for processing performance metrics
    """
    total_time_ms: float
    rule_time_ms: float
    vector_time_ms: float
    rerank_time_ms: float
    cache_time_ms: float
    
    @property
    def rule_percentage(self) -> float:
        """Percentage of time spent on rule-based detection"""
        return (self.rule_time_ms / self.total_time_ms) * 100 if self.total_time_ms > 0 else 0
    
    @property
    def vector_percentage(self) -> float:
        """Percentage of time spent on vector search"""
        return (self.vector_time_ms / self.total_time_ms) * 100 if self.total_time_ms > 0 else 0
    
    @property
    def rerank_percentage(self) -> float:
        """Percentage of time spent on reranking"""
        return (self.rerank_time_ms / self.total_time_ms) * 100 if self.total_time_ms > 0 else 0
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary for serialization"""
        return {
            "total_time_ms": self.total_time_ms,
            "rule_time_ms": self.rule_time_ms,
            "vector_time_ms": self.vector_time_ms,
            "rerank_time_ms": self.rerank_time_ms,
            "cache_time_ms": self.cache_time_ms,
            "rule_percentage": self.rule_percentage,
            "vector_percentage": self.vector_percentage,
            "rerank_percentage": self.rerank_percentage
        }


@dataclass(frozen=True)
class MatchPattern:
    """
    Value object representing a pattern match
    """
    pattern: str
    match_text: str
    start_position: int
    end_position: int
    match_type: str  # 'keyword' or 'regex'
    
    @property
    def length(self) -> int:
        """Length of the matched text"""
        return self.end_position - self.start_position
    
    @property
    def is_at_start(self) -> bool:
        """Whether match is at the beginning of text"""
        return self.start_position == 0
    
    @property
    def relative_position(self) -> float:
        """Relative position in text (0.0 to 1.0)"""
        # This would need the total text length to be meaningful
        # For now, return position as-is
        return float(self.start_position)


@dataclass(frozen=True)
class IntentCandidate:
    """
    Value object representing an intent candidate with all scoring information
    """
    intent_id: IntentId
    confidence_scores: List[ConfidenceScore]
    final_confidence: Confidence
    method_used: str
    metadata: Dict[str, Any]
    
    @property
    def highest_confidence(self) -> ConfidenceScore:
        """Get the highest confidence score"""
        return max(self.confidence_scores, key=lambda x: x.value)
    
    @property
    def score_sources(self) -> List[str]:
        """Get list of all scoring sources"""
        return [score.source for score in self.confidence_scores]
    
    def has_source(self, source: str) -> bool:
        """Check if candidate has score from specific source"""
        return source in self.score_sources
    
    def get_score_by_source(self, source: str) -> Optional[ConfidenceScore]:
        """Get confidence score from specific source"""
        for score in self.confidence_scores:
            if score.source == source:
                return score
        return None


@dataclass(frozen=True)
class DetectionStrategy:
    """
    Value object defining detection strategy configuration
    """
    use_cache: bool = True
    use_rules: bool = True
    use_vector: bool = True
    use_reranker: bool = True
    parallel_execution: bool = False
    early_exit_threshold: float = 0.9
    max_candidates: int = 5
    
    def should_use_early_exit(self, confidence: float) -> bool:
        """Check if early exit should be used"""
        return confidence >= self.early_exit_threshold
    
    def get_enabled_methods(self) -> List[str]:
        """Get list of enabled detection methods"""
        methods = []
        if self.use_cache:
            methods.append("cache")
        if self.use_rules:
            methods.append("rules")
        if self.use_vector:
            methods.append("vector")
        if self.use_reranker:
            methods.append("reranker")
        return methods
