"""
Logging setup and configuration
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional
import structlog

from ..config.settings import LoggingConfig, Environment
from .formatters import J<PERSON><PERSON>ormatter, StructuredFormatter


def setup_logging(
    config: LoggingConfig, 
    environment: Environment = Environment.DEVELOPMENT
) -> None:
    """
    Setup structured logging with proper configuration
    
    Args:
        config: Logging configuration
        environment: Application environment
    """
    
    # Configure structlog
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    # Add JSON processor for production
    if environment == Environment.PRODUCTION or config.format == "json":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.Console<PERSON>er(colors=True))
    
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level.value))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, config.level.value))
    
    if config.format == "json":
        console_formatter = JSONFormatter(
            include_timestamp=config.include_timestamp,
            include_level=config.include_level,
            include_logger_name=config.include_logger_name,
            include_function_name=config.include_function_name,
            include_line_number=config.include_line_number,
            mask_sensitive_data=config.mask_sensitive_data
        )
    else:
        console_formatter = StructuredFormatter(
            include_timestamp=config.include_timestamp,
            include_level=config.include_level,
            include_logger_name=config.include_logger_name,
            include_function_name=config.include_function_name,
            include_line_number=config.include_line_number
        )
    
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if enabled)
    if config.enable_file_logging:
        setup_file_logging(config)
    
    # Set specific logger levels
    _configure_logger_levels(environment)
    
    logger = structlog.get_logger(__name__)
    logger.info(
        "Logging configured",
        level=config.level.value,
        format=config.format,
        file_logging=config.enable_file_logging,
        environment=environment.value
    )


def setup_file_logging(config: LoggingConfig) -> None:
    """Setup file logging with rotation"""
    
    # Create log directory if it doesn't exist
    log_file_path = Path(config.log_file)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Rotating file handler
    file_handler = logging.handlers.RotatingFileHandler(
        filename=config.log_file,
        maxBytes=config.max_file_size,
        backupCount=config.backup_count,
        encoding='utf-8'
    )
    
    file_handler.setLevel(logging.DEBUG)  # File gets all logs
    
    # Always use JSON format for file logs
    file_formatter = JSONFormatter(
        include_timestamp=True,
        include_level=True,
        include_logger_name=True,
        include_function_name=config.include_function_name,
        include_line_number=config.include_line_number,
        mask_sensitive_data=config.mask_sensitive_data
    )
    
    file_handler.setFormatter(file_formatter)
    
    # Add to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)


def _configure_logger_levels(environment: Environment) -> None:
    """Configure specific logger levels"""
    
    # Reduce noise from external libraries
    external_loggers = [
        'urllib3.connectionpool',
        'requests.packages.urllib3',
        'openai',
        'httpx',
        'httpcore',
        'qdrant_client',
        'sentence_transformers'
    ]
    
    for logger_name in external_loggers:
        logger = logging.getLogger(logger_name)
        if environment == Environment.PRODUCTION:
            logger.setLevel(logging.WARNING)
        else:
            logger.setLevel(logging.INFO)
    
    # Set our application loggers
    app_loggers = [
        'src_refactored.core',
        'src_refactored.infrastructure',
        'src_refactored.presentation'
    ]
    
    for logger_name in app_loggers:
        logger = logging.getLogger(logger_name)
        if environment == Environment.DEVELOPMENT:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.INFO)


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """
    Mixin class to add logging capabilities to any class
    """
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """Get logger for this class"""
        return structlog.get_logger(self.__class__.__module__)


class PerformanceLogger:
    """
    Logger for performance metrics and slow operations
    """
    
    def __init__(self, slow_threshold_ms: float = 1000.0):
        self.slow_threshold_ms = slow_threshold_ms
        self.logger = structlog.get_logger("performance")
    
    def log_operation(
        self, 
        operation: str, 
        duration_ms: float, 
        **kwargs
    ) -> None:
        """Log operation performance"""
        
        if duration_ms >= self.slow_threshold_ms:
            self.logger.warning(
                "Slow operation detected",
                operation=operation,
                duration_ms=duration_ms,
                threshold_ms=self.slow_threshold_ms,
                **kwargs
            )
        else:
            self.logger.debug(
                "Operation completed",
                operation=operation,
                duration_ms=duration_ms,
                **kwargs
            )
    
    def log_query_performance(
        self, 
        query: str, 
        method: str, 
        duration_ms: float,
        confidence: float,
        **kwargs
    ) -> None:
        """Log query performance specifically"""
        
        # Hash query for privacy
        import hashlib
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]
        
        log_data = {
            "query_hash": query_hash,
            "method": method,
            "duration_ms": duration_ms,
            "confidence": confidence,
            **kwargs
        }
        
        if duration_ms >= self.slow_threshold_ms:
            self.logger.warning("Slow query detected", **log_data)
        else:
            self.logger.info("Query processed", **log_data)


class SecurityLogger:
    """
    Logger for security-related events
    """
    
    def __init__(self):
        self.logger = structlog.get_logger("security")
    
    def log_invalid_query(self, query: str, reason: str, **kwargs) -> None:
        """Log invalid query attempt"""
        self.logger.warning(
            "Invalid query detected",
            query_length=len(query),
            reason=reason,
            **kwargs
        )
    
    def log_rate_limit_exceeded(self, client_id: str, **kwargs) -> None:
        """Log rate limit exceeded"""
        self.logger.warning(
            "Rate limit exceeded",
            client_id=client_id,
            **kwargs
        )
    
    def log_authentication_failure(self, reason: str, **kwargs) -> None:
        """Log authentication failure"""
        self.logger.warning(
            "Authentication failed",
            reason=reason,
            **kwargs
        )
    
    def log_suspicious_activity(self, activity: str, **kwargs) -> None:
        """Log suspicious activity"""
        self.logger.error(
            "Suspicious activity detected",
            activity=activity,
            **kwargs
        )


# Global logger instances
performance_logger = PerformanceLogger()
security_logger = SecurityLogger()


def log_function_call(func):
    """
    Decorator to log function calls with performance metrics
    """
    import functools
    import time
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        logger = structlog.get_logger(func.__module__)
        start_time = time.time()
        
        logger.debug(
            "Function called",
            function=func.__name__,
            args_count=len(args),
            kwargs_keys=list(kwargs.keys())
        )
        
        try:
            result = await func(*args, **kwargs)
            duration_ms = (time.time() - start_time) * 1000
            
            logger.debug(
                "Function completed",
                function=func.__name__,
                duration_ms=duration_ms,
                success=True
            )
            
            return result
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            logger.error(
                "Function failed",
                function=func.__name__,
                duration_ms=duration_ms,
                error=str(e),
                error_type=type(e).__name__
            )
            
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        logger = structlog.get_logger(func.__module__)
        start_time = time.time()
        
        logger.debug(
            "Function called",
            function=func.__name__,
            args_count=len(args),
            kwargs_keys=list(kwargs.keys())
        )
        
        try:
            result = func(*args, **kwargs)
            duration_ms = (time.time() - start_time) * 1000
            
            logger.debug(
                "Function completed",
                function=func.__name__,
                duration_ms=duration_ms,
                success=True
            )
            
            return result
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            logger.error(
                "Function failed",
                function=func.__name__,
                duration_ms=duration_ms,
                error=str(e),
                error_type=type(e).__name__
            )
            
            raise
    
    # Return appropriate wrapper based on function type
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper
