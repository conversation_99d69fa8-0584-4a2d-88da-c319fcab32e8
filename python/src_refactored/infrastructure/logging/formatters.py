"""
Custom logging formatters for structured logging
"""

import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, Optional
import re


class J<PERSON>NFormatter(logging.Formatter):
    """
    JSON formatter for structured logging
    """
    
    def __init__(
        self,
        include_timestamp: bool = True,
        include_level: bool = True,
        include_logger_name: bool = True,
        include_function_name: bool = False,
        include_line_number: bool = False,
        mask_sensitive_data: bool = True
    ):
        super().__init__()
        self.include_timestamp = include_timestamp
        self.include_level = include_level
        self.include_logger_name = include_logger_name
        self.include_function_name = include_function_name
        self.include_line_number = include_line_number
        self.mask_sensitive_data = mask_sensitive_data
        
        # Sensitive data patterns
        self.sensitive_patterns = [
            (re.compile(r'(api[_-]?key["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***'),
            (re.compile(r'(password["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***'),
            (re.compile(r'(token["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***'),
            (re.compile(r'(secret["\']?\s*[:=]\s*["\']?)([^"\']+)', re.IGNORECASE), r'\1***'),
        ]
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as JSON"""
        
        log_data: Dict[str, Any] = {}
        
        # Basic fields
        if self.include_timestamp:
            log_data['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        
        if self.include_level:
            log_data['level'] = record.levelname
        
        if self.include_logger_name:
            log_data['logger'] = record.name
        
        # Message
        log_data['message'] = record.getMessage()
        
        # Location information
        if self.include_function_name:
            log_data['function'] = record.funcName
        
        if self.include_line_number:
            log_data['line'] = record.lineno
            log_data['file'] = record.pathname
        
        # Exception information
        if record.exc_info:
            log_data['exception'] = {
                'type': record.exc_info[0].__name__ if record.exc_info[0] else None,
                'message': str(record.exc_info[1]) if record.exc_info[1] else None,
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Extra fields from record
        extra_fields = {
            key: value for key, value in record.__dict__.items()
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'exc_info', 'exc_text', 'stack_info',
                'getMessage'
            }
        }
        
        if extra_fields:
            log_data.update(extra_fields)
        
        # Mask sensitive data
        if self.mask_sensitive_data:
            log_data = self._mask_sensitive_data(log_data)
        
        return json.dumps(log_data, ensure_ascii=False, default=self._json_serializer)
    
    def _mask_sensitive_data(self, data: Any) -> Any:
        """Recursively mask sensitive data"""
        if isinstance(data, dict):
            return {key: self._mask_sensitive_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._mask_sensitive_data(item) for item in data]
        elif isinstance(data, str):
            # Apply sensitive data patterns
            masked_data = data
            for pattern, replacement in self.sensitive_patterns:
                masked_data = pattern.sub(replacement, masked_data)
            return masked_data
        else:
            return data
    
    def _json_serializer(self, obj: Any) -> str:
        """Custom JSON serializer for non-serializable objects"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            return str(obj)


class StructuredFormatter(logging.Formatter):
    """
    Human-readable structured formatter for development
    """
    
    def __init__(
        self,
        include_timestamp: bool = True,
        include_level: bool = True,
        include_logger_name: bool = True,
        include_function_name: bool = False,
        include_line_number: bool = False,
        use_colors: bool = True
    ):
        super().__init__()
        self.include_timestamp = include_timestamp
        self.include_level = include_level
        self.include_logger_name = include_logger_name
        self.include_function_name = include_function_name
        self.include_line_number = include_line_number
        self.use_colors = use_colors
        
        # Color codes
        self.colors = {
            'DEBUG': '\033[36m',    # Cyan
            'INFO': '\033[32m',     # Green
            'WARNING': '\033[33m',  # Yellow
            'ERROR': '\033[31m',    # Red
            'CRITICAL': '\033[35m', # Magenta
            'RESET': '\033[0m'      # Reset
        }
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record in human-readable structure"""
        
        parts = []
        
        # Timestamp
        if self.include_timestamp:
            timestamp = datetime.fromtimestamp(record.created).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            parts.append(f"[{timestamp}]")
        
        # Level with color
        if self.include_level:
            level = record.levelname
            if self.use_colors and level in self.colors:
                level = f"{self.colors[level]}{level}{self.colors['RESET']}"
            parts.append(f"[{level}]")
        
        # Logger name
        if self.include_logger_name:
            logger_name = record.name
            # Shorten long logger names
            if len(logger_name) > 30:
                logger_name = "..." + logger_name[-27:]
            parts.append(f"[{logger_name}]")
        
        # Location
        if self.include_function_name or self.include_line_number:
            location_parts = []
            if self.include_function_name:
                location_parts.append(record.funcName)
            if self.include_line_number:
                location_parts.append(f"line {record.lineno}")
            parts.append(f"[{':'.join(location_parts)}]")
        
        # Message
        message = record.getMessage()
        
        # Build the main log line
        prefix = " ".join(parts)
        main_line = f"{prefix} {message}"
        
        # Add extra fields
        extra_fields = {
            key: value for key, value in record.__dict__.items()
            if key not in {
                'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                'filename', 'module', 'lineno', 'funcName', 'created',
                'msecs', 'relativeCreated', 'thread', 'threadName',
                'processName', 'process', 'exc_info', 'exc_text', 'stack_info',
                'getMessage'
            }
        }
        
        lines = [main_line]
        
        # Format extra fields
        if extra_fields:
            for key, value in extra_fields.items():
                if isinstance(value, dict):
                    lines.append(f"  {key}:")
                    for sub_key, sub_value in value.items():
                        lines.append(f"    {sub_key}: {sub_value}")
                elif isinstance(value, list):
                    lines.append(f"  {key}: [{', '.join(str(v) for v in value)}]")
                else:
                    lines.append(f"  {key}: {value}")
        
        # Exception information
        if record.exc_info:
            lines.append("  Exception:")
            exception_lines = traceback.format_exception(*record.exc_info)
            for line in exception_lines:
                lines.append(f"    {line.rstrip()}")
        
        return "\n".join(lines)


class MetricsFormatter(logging.Formatter):
    """
    Specialized formatter for metrics logging
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format metrics log record"""
        
        # Extract metrics data
        metrics_data = getattr(record, 'metrics', {})
        
        # Create metrics line
        timestamp = datetime.utcnow().isoformat() + 'Z'
        
        base_data = {
            'timestamp': timestamp,
            'type': 'metrics',
            'logger': record.name,
            'message': record.getMessage()
        }
        
        # Merge with metrics data
        base_data.update(metrics_data)
        
        return json.dumps(base_data, ensure_ascii=False)


class AuditFormatter(logging.Formatter):
    """
    Specialized formatter for audit logging
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """Format audit log record"""
        
        timestamp = datetime.utcnow().isoformat() + 'Z'
        
        audit_data = {
            'timestamp': timestamp,
            'type': 'audit',
            'level': record.levelname,
            'message': record.getMessage(),
            'user_id': getattr(record, 'user_id', None),
            'session_id': getattr(record, 'session_id', None),
            'action': getattr(record, 'action', None),
            'resource': getattr(record, 'resource', None),
            'result': getattr(record, 'result', None),
            'ip_address': getattr(record, 'ip_address', None),
            'user_agent': getattr(record, 'user_agent', None)
        }
        
        # Remove None values
        audit_data = {k: v for k, v in audit_data.items() if v is not None}
        
        return json.dumps(audit_data, ensure_ascii=False)


def create_formatter(
    format_type: str = "structured",
    **kwargs
) -> logging.Formatter:
    """
    Factory function to create formatters
    
    Args:
        format_type: Type of formatter ('json', 'structured', 'metrics', 'audit')
        **kwargs: Additional formatter arguments
        
    Returns:
        Configured formatter
    """
    
    if format_type == "json":
        return JSONFormatter(**kwargs)
    elif format_type == "structured":
        return StructuredFormatter(**kwargs)
    elif format_type == "metrics":
        return MetricsFormatter(**kwargs)
    elif format_type == "audit":
        return AuditFormatter(**kwargs)
    else:
        raise ValueError(f"Unknown formatter type: {format_type}")


# Utility functions for adding context to log records
def add_request_context(record: logging.LogRecord, request_id: str, user_id: Optional[str] = None) -> None:
    """Add request context to log record"""
    record.request_id = request_id
    if user_id:
        record.user_id = user_id


def add_performance_context(record: logging.LogRecord, operation: str, duration_ms: float) -> None:
    """Add performance context to log record"""
    record.operation = operation
    record.duration_ms = duration_ms


def add_error_context(record: logging.LogRecord, error_code: str, error_details: Dict[str, Any]) -> None:
    """Add error context to log record"""
    record.error_code = error_code
    record.error_details = error_details
