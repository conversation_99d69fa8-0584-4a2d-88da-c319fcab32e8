"""
Local embedding provider using sentence-transformers
"""

import time
from typing import List, Dict, Any
import structlog

from ...core.domain.services import Embedding<PERSON>rovider
from ...core.domain.exceptions import EmbeddingError, ServiceUnavailableError

logger = structlog.get_logger(__name__)

try:
    from sentence_transformers import SentenceTransformer
    import torch
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logger.warning("sentence-transformers not available")


class LocalEmbeddingProvider(EmbeddingProvider):
    """
    Local embedding provider using sentence-transformers
    """
    
    def __init__(
        self,
        model_name: str = "sentence-transformers/all-MiniLM-L6-v2",
        device: str = "cpu",
        batch_size: int = 32,
        normalize_embeddings: bool = True
    ):
        if not SENTENCE_TRANSFORMERS_AVAILABLE:
            raise ServiceUnavailableError(
                "local_embeddings",
                "sentence-transformers not available. Install with: pip install sentence-transformers"
            )
        
        self.model_name = model_name
        self.device = device
        self.batch_size = batch_size
        self.normalize_embeddings = normalize_embeddings
        
        # Initialize model
        self.model = None
        self.model_loaded = False
        self.load_error = None
        
        # Statistics
        self._stats = {
            "requests": 0,
            "texts_processed": 0,
            "errors": 0,
            "total_time": 0.0
        }
        
        # Load model
        self._load_model()
    
    def _load_model(self) -> None:
        """Load the sentence transformer model"""
        try:
            logger.info("Loading sentence transformer model", model=self.model_name)
            start_time = time.time()
            
            self.model = SentenceTransformer(self.model_name, device=self.device)
            
            # Get model info
            self.dimensions = self.model.get_sentence_embedding_dimension()
            
            load_time = time.time() - start_time
            self.model_loaded = True
            
            logger.info(
                "Sentence transformer model loaded",
                model=self.model_name,
                dimensions=self.dimensions,
                device=self.device,
                load_time_seconds=load_time
            )
            
        except Exception as e:
            self.load_error = str(e)
            logger.error(
                "Failed to load sentence transformer model",
                model=self.model_name,
                error=str(e)
            )
    
    async def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for single text
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        if not self.model_loaded:
            raise EmbeddingError(
                text, 
                f"Model not loaded: {self.load_error or 'Unknown error'}"
            )
        
        try:
            start_time = time.time()
            
            # Validate input
            if not text or not text.strip():
                raise EmbeddingError(text, "Text cannot be empty")
            
            # Generate embedding
            embedding = self.model.encode(
                [text.strip()],
                batch_size=1,
                normalize_embeddings=self.normalize_embeddings,
                convert_to_numpy=True
            )[0]
            
            # Convert to list
            embedding_list = embedding.tolist()
            
            # Update statistics
            processing_time = time.time() - start_time
            self._stats["requests"] += 1
            self._stats["texts_processed"] += 1
            self._stats["total_time"] += processing_time
            
            logger.debug(
                "Local embedding generated",
                text_length=len(text),
                embedding_dimension=len(embedding_list),
                processing_time_ms=processing_time * 1000
            )
            
            return embedding_list
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error(
                "Local embedding generation failed",
                text_length=len(text) if text else 0,
                error=str(e),
                error_type=type(e).__name__
            )
            raise EmbeddingError(text, str(e))
    
    async def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts
        
        Args:
            texts: List of input texts
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        if not self.model_loaded:
            raise EmbeddingError(
                f"batch of {len(texts)} texts",
                f"Model not loaded: {self.load_error or 'Unknown error'}"
            )
        
        try:
            start_time = time.time()
            
            # Clean texts
            cleaned_texts = [text.strip() for text in texts if text and text.strip()]
            
            if not cleaned_texts:
                # Return zero embeddings for empty texts
                return [[0.0] * self.dimensions] * len(texts)
            
            # Generate embeddings in batches
            all_embeddings = []
            
            for i in range(0, len(cleaned_texts), self.batch_size):
                batch = cleaned_texts[i:i + self.batch_size]
                
                batch_start = time.time()
                batch_embeddings = self.model.encode(
                    batch,
                    batch_size=len(batch),
                    normalize_embeddings=self.normalize_embeddings,
                    convert_to_numpy=True,
                    show_progress_bar=False
                )
                
                # Convert to lists
                batch_embeddings_list = [emb.tolist() for emb in batch_embeddings]
                all_embeddings.extend(batch_embeddings_list)
                
                batch_time = time.time() - batch_start
                logger.debug(
                    "Local batch embedding completed",
                    batch_size=len(batch),
                    batch_time_ms=batch_time * 1000
                )
            
            total_time = time.time() - start_time
            
            # Update statistics
            self._stats["requests"] += 1
            self._stats["texts_processed"] += len(cleaned_texts)
            self._stats["total_time"] += total_time
            
            logger.info(
                "Local batch embedding completed",
                total_texts=len(texts),
                processed_texts=len(cleaned_texts),
                total_embeddings=len(all_embeddings),
                total_time_ms=total_time * 1000,
                avg_time_per_text=total_time / len(texts) * 1000
            )
            
            return all_embeddings
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error(
                "Local batch embedding failed",
                batch_size=len(texts),
                error=str(e),
                error_type=type(e).__name__
            )
            raise EmbeddingError(f"batch of {len(texts)} texts", str(e))
    
    def get_embedding_dimension(self) -> int:
        """Get embedding vector dimension"""
        if self.model_loaded:
            return self.dimensions
        else:
            # Return default dimension for common models
            default_dimensions = {
                "sentence-transformers/all-MiniLM-L6-v2": 384,
                "sentence-transformers/all-mpnet-base-v2": 768,
                "sentence-transformers/all-MiniLM-L12-v2": 384,
            }
            return default_dimensions.get(self.model_name, 384)
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get embedding model information"""
        info = {
            "provider": "local",
            "model": self.model_name,
            "device": self.device,
            "batch_size": self.batch_size,
            "normalize_embeddings": self.normalize_embeddings,
            "model_loaded": self.model_loaded,
            "statistics": self._stats.copy()
        }
        
        if self.model_loaded:
            info["dimensions"] = self.dimensions
            info["max_seq_length"] = getattr(self.model, 'max_seq_length', None)
        else:
            info["load_error"] = self.load_error
        
        return info
    
    async def health_check(self) -> Dict[str, Any]:
        """Check local model health"""
        if not self.model_loaded:
            return {
                "status": "unhealthy",
                "model": self.model_name,
                "error": self.load_error or "Model not loaded",
                "test_successful": False
            }
        
        try:
            # Test with a simple embedding
            test_embedding = await self.embed_text("test")
            
            return {
                "status": "healthy",
                "model": self.model_name,
                "dimensions": len(test_embedding),
                "device": self.device,
                "test_successful": True
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "model": self.model_name,
                "error": str(e),
                "test_successful": False
            }
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get detailed usage statistics"""
        total_requests = self._stats["requests"]
        
        return {
            "total_requests": total_requests,
            "total_texts_processed": self._stats["texts_processed"],
            "total_errors": self._stats["errors"],
            "total_time_seconds": self._stats["total_time"],
            "avg_time_per_request": (
                self._stats["total_time"] / total_requests 
                if total_requests > 0 else 0
            ),
            "avg_texts_per_request": (
                self._stats["texts_processed"] / total_requests 
                if total_requests > 0 else 0
            ),
            "error_rate": (
                self._stats["errors"] / total_requests * 100 
                if total_requests > 0 else 0
            )
        }
    
    def reset_stats(self) -> None:
        """Reset usage statistics"""
        self._stats = {
            "requests": 0,
            "texts_processed": 0,
            "errors": 0,
            "total_time": 0.0
        }
        logger.info("Local embedding provider stats reset")
    
    def get_device_info(self) -> Dict[str, Any]:
        """Get device information"""
        info = {
            "device": self.device,
            "torch_available": SENTENCE_TRANSFORMERS_AVAILABLE
        }
        
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            info.update({
                "cuda_available": torch.cuda.is_available(),
                "cuda_device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
                "current_device": str(torch.cuda.current_device()) if torch.cuda.is_available() else "cpu"
            })
            
            if torch.cuda.is_available():
                info["cuda_device_name"] = torch.cuda.get_device_name()
                info["cuda_memory_allocated"] = torch.cuda.memory_allocated()
                info["cuda_memory_cached"] = torch.cuda.memory_reserved()
        
        return info
    
    def reload_model(self) -> None:
        """Reload the model"""
        logger.info("Reloading sentence transformer model")
        self.model_loaded = False
        self.load_error = None
        self._load_model()
