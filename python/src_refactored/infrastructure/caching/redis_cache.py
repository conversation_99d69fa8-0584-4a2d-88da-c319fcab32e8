"""
Redis cache implementation
"""

import json
import pickle
from typing import Optional, Any, Dict
import structlog

from core.domain.services import CacheService
from core.domain.exceptions import CacheError, ServiceUnavailableError
from shared.types import CacheKey

logger = structlog.get_logger(__name__)

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("redis library not available")


class RedisCacheService(CacheService):
    """
    Redis cache implementation with connection pooling
    """
    
    def __init__(
        self,
        redis_url: str = "redis://localhost:6379/0",
        key_prefix: str = "fpt_agent:",
        default_ttl: int = 300,
        max_connections: int = 10,
        serialization: str = "json"  # "json" or "pickle"
    ):
        if not REDIS_AVAILABLE:
            raise ServiceUnavailableError(
                "redis_cache",
                "Redis library not available. Install with: pip install redis"
            )
        
        self.redis_url = redis_url
        self.key_prefix = key_prefix
        self.default_ttl = default_ttl
        self.serialization = serialization
        
        # Connection pool
        self.pool = redis.ConnectionPool.from_url(
            redis_url,
            max_connections=max_connections,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={}
        )
        
        self.redis_client = redis.Redis(connection_pool=self.pool)
        
        # Statistics
        self._stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "errors": 0
        }
        
        logger.info(
            "Redis cache initialized",
            redis_url=redis_url,
            key_prefix=key_prefix,
            default_ttl=default_ttl,
            serialization=serialization
        )
    
    def _make_key(self, key: CacheKey) -> str:
        """Create prefixed key"""
        return f"{self.key_prefix}{key}"
    
    def _serialize(self, value: Any) -> bytes:
        """Serialize value for storage"""
        if self.serialization == "json":
            return json.dumps(value, ensure_ascii=False).encode('utf-8')
        elif self.serialization == "pickle":
            return pickle.dumps(value)
        else:
            raise ValueError(f"Unknown serialization method: {self.serialization}")
    
    def _deserialize(self, data: bytes) -> Any:
        """Deserialize value from storage"""
        if self.serialization == "json":
            return json.loads(data.decode('utf-8'))
        elif self.serialization == "pickle":
            return pickle.loads(data)
        else:
            raise ValueError(f"Unknown serialization method: {self.serialization}")
    
    async def get(self, key: CacheKey) -> Optional[Any]:
        """Get value from Redis cache"""
        try:
            redis_key = self._make_key(key)
            data = await self.redis_client.get(redis_key)
            
            if data is not None:
                value = self._deserialize(data)
                self._stats["hits"] += 1
                logger.debug("Redis cache hit", key=key)
                return value
            else:
                self._stats["misses"] += 1
                logger.debug("Redis cache miss", key=key)
                return None
                
        except Exception as e:
            self._stats["errors"] += 1
            logger.error("Redis cache get failed", key=key, error=str(e))
            raise CacheError("get", key, str(e))
    
    async def set(
        self, 
        key: CacheKey, 
        value: Any, 
        ttl_seconds: Optional[int] = None
    ) -> None:
        """Set value in Redis cache with optional TTL"""
        try:
            redis_key = self._make_key(key)
            serialized_value = self._serialize(value)
            ttl = ttl_seconds or self.default_ttl
            
            await self.redis_client.setex(redis_key, ttl, serialized_value)
            self._stats["sets"] += 1
            
            logger.debug(
                "Redis cache set",
                key=key,
                ttl_seconds=ttl,
                data_size=len(serialized_value)
            )
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error("Redis cache set failed", key=key, error=str(e))
            raise CacheError("set", key, str(e))
    
    async def delete(self, key: CacheKey) -> None:
        """Delete key from Redis cache"""
        try:
            redis_key = self._make_key(key)
            deleted = await self.redis_client.delete(redis_key)
            
            if deleted:
                self._stats["deletes"] += 1
                logger.debug("Redis cache delete", key=key)
            else:
                logger.debug("Redis cache delete - key not found", key=key)
                
        except Exception as e:
            self._stats["errors"] += 1
            logger.error("Redis cache delete failed", key=key, error=str(e))
            raise CacheError("delete", key, str(e))
    
    async def clear(self) -> None:
        """Clear all cache entries with our prefix"""
        try:
            # Find all keys with our prefix
            pattern = f"{self.key_prefix}*"
            keys = []
            
            async for key in self.redis_client.scan_iter(match=pattern):
                keys.append(key)
            
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info("Redis cache cleared", cleared_entries=deleted)
            else:
                logger.info("Redis cache clear - no entries found")
                
        except Exception as e:
            self._stats["errors"] += 1
            logger.error("Redis cache clear failed", error=str(e))
            raise CacheError("clear", "", str(e))
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            # Get Redis info
            redis_info = await self.redis_client.info()
            
            # Count our keys
            pattern = f"{self.key_prefix}*"
            key_count = 0
            async for _ in self.redis_client.scan_iter(match=pattern):
                key_count += 1
            
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = (self._stats["hits"] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                "type": "redis",
                "key_count": key_count,
                "hit_rate": round(hit_rate, 2),
                "redis_memory_used": redis_info.get("used_memory_human", "unknown"),
                "redis_connected_clients": redis_info.get("connected_clients", 0),
                "redis_uptime": redis_info.get("uptime_in_seconds", 0),
                **self._stats
            }
            
        except Exception as e:
            logger.error("Failed to get Redis stats", error=str(e))
            return {
                "type": "redis",
                "error": str(e),
                **self._stats
            }
    
    async def ping(self) -> bool:
        """Test Redis connection"""
        try:
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error("Redis ping failed", error=str(e))
            return False
    
    async def get_memory_usage(self, key: CacheKey) -> Optional[int]:
        """Get memory usage for a specific key"""
        try:
            redis_key = self._make_key(key)
            memory_usage = await self.redis_client.memory_usage(redis_key)
            return memory_usage
        except Exception as e:
            logger.warning("Failed to get memory usage", key=key, error=str(e))
            return None
    
    async def set_multiple(
        self, 
        items: Dict[CacheKey, Any], 
        ttl_seconds: Optional[int] = None
    ) -> None:
        """Set multiple key-value pairs efficiently"""
        try:
            ttl = ttl_seconds or self.default_ttl
            
            # Use pipeline for efficiency
            pipe = self.redis_client.pipeline()
            
            for key, value in items.items():
                redis_key = self._make_key(key)
                serialized_value = self._serialize(value)
                pipe.setex(redis_key, ttl, serialized_value)
            
            await pipe.execute()
            self._stats["sets"] += len(items)
            
            logger.debug(
                "Redis cache set multiple",
                count=len(items),
                ttl_seconds=ttl
            )
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error("Redis cache set multiple failed", error=str(e))
            raise CacheError("set_multiple", "", str(e))
    
    async def get_multiple(self, keys: list[CacheKey]) -> Dict[CacheKey, Any]:
        """Get multiple values efficiently"""
        try:
            redis_keys = [self._make_key(key) for key in keys]
            values = await self.redis_client.mget(redis_keys)
            
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    result[key] = self._deserialize(value)
                    self._stats["hits"] += 1
                else:
                    self._stats["misses"] += 1
            
            logger.debug(
                "Redis cache get multiple",
                requested=len(keys),
                found=len(result)
            )
            
            return result
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error("Redis cache get multiple failed", error=str(e))
            raise CacheError("get_multiple", "", str(e))
    
    async def close(self) -> None:
        """Close Redis connection"""
        try:
            await self.redis_client.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error("Failed to close Redis connection", error=str(e))
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
