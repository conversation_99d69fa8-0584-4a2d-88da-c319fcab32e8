"""
Reranking service implementation using CrossEncoder
"""

import time
from typing import List, Optional, Dict, Any, Tuple
import structlog

from ...core.domain.entities import SearchCandidate
from ...core.domain.services import RerankerService
from ...core.domain.exceptions import RerankerError, ServiceUnavailableError
from ...shared.types import QueryText

logger = structlog.get_logger(__name__)

try:
    from sentence_transformers import CrossEncoder
    CROSS_ENCODER_AVAILABLE = True
except ImportError:
    CROSS_ENCODER_AVAILABLE = False
    logger.warning("sentence-transformers not available, reranker will be disabled")


class RerankerServiceImpl(RerankerService):
    """
    Implementation of reranking service using CrossEncoder
    """
    
    def __init__(
        self,
        model_name: str = 'cross-encoder/ms-marco-MiniLM-L-6-v2',
        score_threshold: float = 2.0,
        max_candidates: int = 10,
        enable_caching: bool = True
    ):
        self.model_name = model_name
        self.score_threshold = score_threshold
        self.max_candidates = max_candidates
        self.enable_caching = enable_caching
        
        self.cross_encoder: Optional[CrossEncoder] = None
        self.model_loaded = False
        self.load_error: Optional[str] = None
        
        # Simple cache for reranking results
        self._cache: Dict[str, List[SearchCandidate]] = {}
        self._cache_max_size = 1000
        
        # Initialize model
        self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the CrossEncoder model"""
        if not CROSS_ENCODER_AVAILABLE:
            self.load_error = "sentence-transformers library not available"
            logger.error("Cannot initialize reranker", error=self.load_error)
            return
        
        try:
            logger.info("Loading CrossEncoder model", model_name=self.model_name)
            start_time = time.time()
            
            self.cross_encoder = CrossEncoder(self.model_name)
            load_time = (time.time() - start_time) * 1000
            
            self.model_loaded = True
            logger.info(
                "CrossEncoder model loaded successfully",
                model_name=self.model_name,
                load_time_ms=load_time
            )
            
        except Exception as e:
            self.load_error = str(e)
            logger.error(
                "Failed to load CrossEncoder model",
                model_name=self.model_name,
                error=str(e)
            )
    
    async def rerank(
        self, 
        query: QueryText, 
        candidates: List[SearchCandidate]
    ) -> List[SearchCandidate]:
        """
        Rerank search candidates using CrossEncoder
        
        Args:
            query: Original query
            candidates: Search candidates to rerank
            
        Returns:
            Reranked candidates with updated scores
        """
        if not self.model_loaded:
            if self.load_error:
                raise ServiceUnavailableError(
                    "reranker", 
                    f"Model not loaded: {self.load_error}"
                )
            else:
                raise ServiceUnavailableError("reranker", "Model not initialized")
        
        if not candidates:
            logger.debug("No candidates to rerank")
            return []
        
        # Limit candidates to avoid performance issues
        limited_candidates = candidates[:self.max_candidates]
        
        # Check cache
        cache_key = self._get_cache_key(query, limited_candidates)
        if self.enable_caching and cache_key in self._cache:
            logger.debug("Returning cached reranking result", cache_key=cache_key[:16])
            return self._cache[cache_key]
        
        start_time = time.time()
        
        logger.debug(
            "Starting reranking",
            query=query,
            candidate_count=len(limited_candidates),
            threshold=self.score_threshold
        )
        
        try:
            # Prepare sentence pairs for CrossEncoder
            sentence_pairs = [
                (query, candidate.text) 
                for candidate in limited_candidates
            ]
            
            # Get reranking scores
            rerank_start = time.time()
            scores = self.cross_encoder.predict(sentence_pairs)
            rerank_time = (time.time() - rerank_start) * 1000
            
            # Create reranked candidates with new scores
            reranked_candidates = []
            
            logger.debug("Reranker scores computed", scores_count=len(scores))
            
            for i, (candidate, rerank_score) in enumerate(zip(limited_candidates, scores)):
                # Log individual scores for debugging
                logger.debug(
                    "Candidate score",
                    rank=i+1,
                    intent_id=candidate.intent_id,
                    original_score=round(candidate.score, 4),
                    rerank_score=round(float(rerank_score), 4),
                    text_preview=candidate.text[:50] + "..." if len(candidate.text) > 50 else candidate.text
                )
                
                # Only include candidates above threshold
                if float(rerank_score) >= self.score_threshold:
                    # Create new candidate with reranker score
                    reranked_candidate = SearchCandidate(
                        text=candidate.text,
                        intent_id=candidate.intent_id,
                        score=float(rerank_score),  # Use reranker score
                        metadata={
                            **candidate.metadata,
                            "original_score": candidate.score,
                            "rerank_score": float(rerank_score),
                            "reranked": True
                        },
                        source=f"{candidate.source}_reranked"
                    )
                    reranked_candidates.append(reranked_candidate)
            
            # Sort by reranker score (descending)
            reranked_candidates.sort(key=lambda x: x.score, reverse=True)
            
            processing_time = (time.time() - start_time) * 1000
            
            logger.info(
                "Reranking completed",
                original_count=len(limited_candidates),
                reranked_count=len(reranked_candidates),
                threshold=self.score_threshold,
                rerank_time_ms=rerank_time,
                total_time_ms=processing_time
            )
            
            # Log top results
            if reranked_candidates:
                top_results = reranked_candidates[:3]
                logger.debug(
                    "Top reranked results",
                    results=[
                        {
                            "intent_id": c.intent_id,
                            "score": round(c.score, 3),
                            "original_score": round(c.metadata.get("original_score", 0), 3)
                        }
                        for c in top_results
                    ]
                )
            
            # Cache result
            if self.enable_caching:
                self._cache_result(cache_key, reranked_candidates)
            
            return reranked_candidates
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(
                "Reranking failed",
                error=str(e),
                error_type=type(e).__name__,
                candidate_count=len(limited_candidates),
                processing_time_ms=processing_time
            )
            
            raise RerankerError(str(e), len(limited_candidates))
    
    def _get_cache_key(self, query: str, candidates: List[SearchCandidate]) -> str:
        """Generate cache key for query and candidates"""
        import hashlib
        
        # Create a hash of query + candidate texts
        content = query + "|" + "|".join(c.text for c in candidates)
        return hashlib.md5(content.encode()).hexdigest()
    
    def _cache_result(self, cache_key: str, result: List[SearchCandidate]) -> None:
        """Cache reranking result"""
        # Simple LRU-like cache management
        if len(self._cache) >= self._cache_max_size:
            # Remove oldest entry (first key)
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
        
        self._cache[cache_key] = result
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get reranker model information"""
        return {
            "model_name": self.model_name,
            "model_loaded": self.model_loaded,
            "load_error": self.load_error,
            "score_threshold": self.score_threshold,
            "max_candidates": self.max_candidates,
            "cache_enabled": self.enable_caching,
            "cache_size": len(self._cache),
            "library_available": CROSS_ENCODER_AVAILABLE
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check reranker service health"""
        health_status = {
            "status": "healthy" if self.model_loaded else "unhealthy",
            "model_info": self.get_model_info(),
            "timestamp": time.time()
        }
        
        if not self.model_loaded:
            health_status["error"] = self.load_error or "Model not loaded"
        
        # Test reranking with dummy data
        if self.model_loaded:
            try:
                dummy_candidates = [
                    SearchCandidate(
                        text="test candidate",
                        intent_id="test_intent",
                        score=0.5
                    )
                ]
                
                test_start = time.time()
                await self.rerank("test query", dummy_candidates)
                test_time = (time.time() - test_start) * 1000
                
                health_status["test_rerank_time_ms"] = test_time
                
            except Exception as e:
                health_status["status"] = "unhealthy"
                health_status["test_error"] = str(e)
        
        return health_status
    
    def clear_cache(self) -> None:
        """Clear reranking cache"""
        self._cache.clear()
        logger.info("Reranker cache cleared")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "cache_size": len(self._cache),
            "cache_max_size": self._cache_max_size,
            "cache_enabled": self.enable_caching
        }
