"""
Hybrid intent detection service - orchestrates all detection methods
"""

import time
import asyncio
from typing import List, Optional, Dict, Any
import structlog

from core.domain.entities import IntentResult, DetectionContext
from core.domain.services import (
    IntentDetectionService, RuleBasedDetector, VectorStore,
    EmbeddingProvider, RerankerService, CacheService
)
from core.domain.exceptions import (
    IntentDetectionError, InvalidQueryError, ServiceUnavailableError
)
from core.domain.value_objects import (
    QueryAnalysis, ConfidenceScore, ProcessingMetrics, DetectionStrategy
)
from shared.types import Result, DetectionMethod
from shared.utils.validation import validate_query, sanitize_query
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl, PerformanceTimer
from infrastructure.config.settings import AppConfig
from infrastructure.intent_detection.vector_based import VectorBasedDetectorImpl

logger = structlog.get_logger(__name__)


class HybridIntentDetectionService(IntentDetectionService):
    """
    Hybrid intent detection service that orchestrates multiple detection methods
    """
    
    def __init__(
        self,
        config: AppConfig,
        rule_detector: RuleBasedDetector,
        vector_store: VectorStore,
        embedding_provider: EmbeddingProvider,
        reranker_service: RerankerService,
        cache_service: CacheService,
        text_processor: Optional[VietnameseTextProcessor] = None,
        metrics_collector: Optional[MetricsCollectorImpl] = None
    ):
        self.config = config
        self.rule_detector = rule_detector
        self.vector_detector = VectorBasedDetectorImpl(
            vector_store=vector_store,
            embedding_provider=embedding_provider,
            default_top_k=config.intent_detection.vector_top_k,
            similarity_threshold=config.intent_detection.thresholds.medium
        )
        self.reranker_service = reranker_service
        self.cache_service = cache_service
        self.text_processor = text_processor or VietnameseTextProcessor()
        self.metrics_collector = metrics_collector or MetricsCollectorImpl()
        
        # Detection strategy
        self.strategy = DetectionStrategy(
            use_cache=config.intent_detection.enable_cache,
            use_rules=config.intent_detection.enable_rules,
            use_vector=config.intent_detection.enable_vector,
            use_reranker=config.intent_detection.enable_reranker,
            parallel_execution=config.intent_detection.parallel_execution,
            early_exit_threshold=config.intent_detection.early_exit_threshold,
            max_candidates=config.intent_detection.max_candidates
        )
        
        logger.info(
            "Hybrid intent detection service initialized",
            strategy=self.strategy.get_enabled_methods(),
            early_exit_threshold=self.strategy.early_exit_threshold,
            max_candidates=self.strategy.max_candidates
        )
    
    async def detect(self, context: DetectionContext) -> Result[IntentResult]:
        """
        Detect intent using hybrid approach
        
        Args:
            context: Detection context with query and metadata
            
        Returns:
            Result containing IntentResult or error
        """
        start_time = time.time()
        query = context.query
        
        # Increment request counter
        self.metrics_collector.increment_counter(
            "intent_detection_requests",
            tags={"language": context.language}
        )
        
        try:
            # Validate and sanitize query
            validate_query(query)
            sanitized_query = sanitize_query(query)
            
            # Analyze query
            query_analysis = QueryAnalysis.analyze(sanitized_query)
            
            logger.info(
                "Intent detection started",
                query_hash=self._hash_query(query),
                language=query_analysis.language,
                word_count=query_analysis.word_count,
                is_mixed_language=query_analysis.is_mixed_language
            )
            
            # Check for irrelevant queries first
            if self.text_processor.is_irrelevant_query(sanitized_query):
                result = self._create_fallback_result(0.1, "irrelevant_query")
                processing_time = (time.time() - start_time) * 1000
                
                self.metrics_collector.record_histogram(
                    "intent_detection_duration",
                    processing_time,
                    tags={"method": "fallback", "reason": "irrelevant"}
                )
                
                logger.info(
                    "Irrelevant query detected",
                    processing_time_ms=processing_time
                )
                
                return Result.ok(result)
            
            # Step 1: Check cache
            if self.strategy.use_cache:
                with PerformanceTimer(self.metrics_collector, "cache_lookup_duration"):
                    cached_result = await self._check_cache(sanitized_query)
                    if cached_result:
                        processing_time = (time.time() - start_time) * 1000
                        
                        self.metrics_collector.increment_counter("cache_hits")
                        self.metrics_collector.record_histogram(
                            "intent_detection_duration",
                            processing_time,
                            tags={"method": "cache"}
                        )
                        
                        logger.info(
                            "Cache hit",
                            intent_id=cached_result.id,
                            confidence=cached_result.confidence,
                            processing_time_ms=processing_time
                        )
                        
                        return Result.ok(cached_result)
            
            self.metrics_collector.increment_counter("cache_misses")
            
            # Step 2: Parallel or sequential detection
            if self.strategy.parallel_execution:
                result = await self._detect_parallel(sanitized_query, query_analysis)
            else:
                result = await self._detect_sequential(sanitized_query, query_analysis)
            
            # Cache successful result
            if self.strategy.use_cache and result.confidence >= 0.3:
                await self._cache_result(sanitized_query, result)
            
            processing_time = (time.time() - start_time) * 1000
            result = result.with_metadata(processing_time_ms=processing_time)
            
            self.metrics_collector.record_histogram(
                "intent_detection_duration",
                processing_time,
                tags={"method": result.method}
            )
            
            self.metrics_collector.increment_counter(
                "intent_detection_success",
                tags={
                    "method": result.method,
                    "confidence_level": result.confidence_level.value
                }
            )
            
            logger.info(
                "Intent detection completed",
                intent_id=result.id,
                confidence=result.confidence,
                method=result.method,
                processing_time_ms=processing_time
            )
            
            return Result.ok(result)
            
        except InvalidQueryError as e:
            self.metrics_collector.increment_counter("invalid_queries")
            logger.warning("Invalid query", error=str(e))
            return Result.error(str(e), "INVALID_QUERY")
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            self.metrics_collector.increment_counter("intent_detection_errors")
            self.metrics_collector.record_histogram(
                "intent_detection_duration",
                processing_time,
                tags={"method": "error"}
            )
            
            logger.error(
                "Intent detection failed",
                error=str(e),
                error_type=type(e).__name__,
                processing_time_ms=processing_time
            )
            
            return Result.error(str(e), "DETECTION_ERROR")
    
    async def _detect_sequential(
        self, 
        query: str, 
        query_analysis: QueryAnalysis
    ) -> IntentResult:
        """Sequential detection: rule → vector → rerank"""
        
        # Step 1: Rule-based detection
        rule_result = None
        if self.strategy.use_rules:
            with PerformanceTimer(self.metrics_collector, "rule_detection_duration"):
                rule_match = await self.rule_detector.detect(query)
                if rule_match:
                    confidence_score = ConfidenceScore.from_rule_match(
                        rule_match.score,
                        rule_match.matched_keywords + rule_match.matched_patterns
                    )
                    
                    rule_result = IntentResult(
                        id=rule_match.intent_id,
                        confidence=confidence_score.value,
                        method=DetectionMethod.RULE,
                        metadata={
                            "matched_keywords": rule_match.matched_keywords,
                            "matched_patterns": rule_match.matched_patterns,
                            "rule_weight": rule_match.weight,
                            "match_position": rule_match.position
                        }
                    )
                    
                    # Early exit for high confidence
                    if self.strategy.should_use_early_exit(rule_result.confidence):
                        logger.debug("Early exit - high confidence rule match")
                        return rule_result
        
        # Step 2: Vector-based detection
        vector_candidates = []
        if self.strategy.use_vector:
            with PerformanceTimer(self.metrics_collector, "vector_detection_duration"):
                vector_candidates = await self.vector_detector.detect(
                    query=query,
                    top_k=self.strategy.max_candidates
                )
        
        # Step 3: Reranking
        final_candidates = vector_candidates
        if self.strategy.use_reranker and vector_candidates:
            with PerformanceTimer(self.metrics_collector, "reranker_duration"):
                try:
                    final_candidates = await self.reranker_service.rerank(
                        query, vector_candidates
                    )
                except Exception as e:
                    logger.warning("Reranking failed, using vector results", error=str(e))
        
        # Combine results
        return self._combine_results(rule_result, final_candidates)
    
    async def _detect_parallel(
        self, 
        query: str, 
        query_analysis: QueryAnalysis
    ) -> IntentResult:
        """Parallel detection: rule + vector simultaneously"""
        
        tasks = []
        
        # Rule-based detection task
        if self.strategy.use_rules:
            tasks.append(self._rule_detection_task(query))
        
        # Vector-based detection task
        if self.strategy.use_vector:
            tasks.append(self._vector_detection_task(query))
        
        # Wait for all tasks
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        rule_result = None
        vector_candidates = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.warning(f"Detection task {i} failed", error=str(result))
                continue
            
            if isinstance(result, IntentResult):
                rule_result = result
            elif isinstance(result, list):
                vector_candidates = result
        
        # Reranking (sequential after parallel detection)
        final_candidates = vector_candidates
        if self.strategy.use_reranker and vector_candidates:
            try:
                final_candidates = await self.reranker_service.rerank(
                    query, vector_candidates
                )
            except Exception as e:
                logger.warning("Reranking failed", error=str(e))
        
        return self._combine_results(rule_result, final_candidates)
    
    async def _rule_detection_task(self, query: str) -> Optional[IntentResult]:
        """Rule detection task for parallel execution"""
        try:
            rule_match = await self.rule_detector.detect(query)
            if rule_match:
                return IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight
                    }
                )
        except Exception as e:
            logger.warning("Rule detection task failed", error=str(e))
        
        return None
    
    async def _vector_detection_task(self, query: str) -> List:
        """Vector detection task for parallel execution"""
        try:
            return await self.vector_detector.detect(
                query=query,
                top_k=self.strategy.max_candidates
            )
        except Exception as e:
            logger.warning("Vector detection task failed", error=str(e))
            return []
    
    def _combine_results(
        self, 
        rule_result: Optional[IntentResult], 
        vector_candidates: List
    ) -> IntentResult:
        """Combine rule and vector results"""
        
        # If we have a high-confidence rule result, use it
        if (rule_result and 
            rule_result.confidence >= self.config.intent_detection.thresholds.high):
            return rule_result
        
        # If we have vector candidates, use the best one
        if vector_candidates:
            best_candidate = vector_candidates[0]
            vector_result = IntentResult(
                id=best_candidate.intent_id,
                confidence=best_candidate.normalized_score,
                method=DetectionMethod.RERANK if hasattr(best_candidate, 'reranked') else DetectionMethod.VECTOR,
                metadata={
                    "vector_score": best_candidate.score,
                    "candidate_count": len(vector_candidates),
                    "source": best_candidate.source
                }
            )
            
            # If we also have a rule result, compare them
            if rule_result:
                if rule_result.confidence > vector_result.confidence:
                    return rule_result.with_metadata(
                        vector_alternative=vector_result.id,
                        vector_confidence=vector_result.confidence
                    )
                else:
                    return vector_result.with_metadata(
                        rule_alternative=rule_result.id,
                        rule_confidence=rule_result.confidence
                    )
            
            return vector_result
        
        # If we only have a rule result (even low confidence), use it
        if rule_result:
            return rule_result
        
        # Fallback
        return self._create_fallback_result(0.1, "no_matches")
    
    def _create_fallback_result(self, confidence: float, reason: str) -> IntentResult:
        """Create fallback result"""
        return IntentResult(
            id="general_info",
            confidence=confidence,
            method=DetectionMethod.FALLBACK,
            metadata={"fallback_reason": reason}
        )
    
    async def _check_cache(self, query: str) -> Optional[IntentResult]:
        """Check cache for query result"""
        try:
            cache_key = self._get_cache_key(query)
            cached_data = await self.cache_service.get(cache_key)
            
            if cached_data:
                return IntentResult(**cached_data)
        except Exception as e:
            logger.warning("Cache check failed", error=str(e))
        
        return None
    
    async def _cache_result(self, query: str, result: IntentResult) -> None:
        """Cache detection result"""
        try:
            cache_key = self._get_cache_key(query)
            cache_data = {
                "id": result.id,
                "confidence": result.confidence,
                "method": result.method,
                "metadata": result.metadata,
                "timestamp": result.timestamp.isoformat()
            }
            
            await self.cache_service.set(
                cache_key, 
                cache_data, 
                ttl_seconds=self.config.cache.default_ttl
            )
        except Exception as e:
            logger.warning("Cache set failed", error=str(e))
    
    def _get_cache_key(self, query: str) -> str:
        """Generate cache key for query"""
        import hashlib
        query_hash = hashlib.md5(query.encode()).hexdigest()
        return f"{self.config.cache.key_prefix}intent:{query_hash}"
    
    def _hash_query(self, query: str) -> str:
        """Generate short hash for query logging"""
        import hashlib
        return hashlib.md5(query.encode()).hexdigest()[:8]
    
    async def detect_batch(
        self, 
        contexts: List[DetectionContext]
    ) -> List[Result[IntentResult]]:
        """Detect intents for multiple queries in batch"""
        start_time = time.time()
        
        logger.info("Batch intent detection started", batch_size=len(contexts))
        
        # Process all contexts concurrently
        tasks = [self.detect(context) for context in contexts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to error results
        final_results = []
        for result in results:
            if isinstance(result, Exception):
                final_results.append(Result.error(str(result), "BATCH_ERROR"))
            else:
                final_results.append(result)
        
        processing_time = (time.time() - start_time) * 1000
        successful_results = sum(1 for r in final_results if r.is_ok())
        
        logger.info(
            "Batch intent detection completed",
            batch_size=len(contexts),
            successful_results=successful_results,
            processing_time_ms=processing_time,
            avg_time_per_query=processing_time / len(contexts) if contexts else 0
        )
        
        return final_results
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        return self.metrics_collector.get_all_metrics()
    
    async def health_check(self) -> Dict[str, Any]:
        """Check service health"""
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "components": {}
        }
        
        # Check rule detector
        try:
            rules = await self.rule_detector.get_rules()
            health_status["components"]["rule_detector"] = {
                "status": "healthy",
                "rule_count": len(rules)
            }
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["components"]["rule_detector"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Check vector detector
        try:
            vector_health = await self.vector_detector.health_check()
            health_status["components"]["vector_detector"] = vector_health
            if vector_health["status"] != "healthy":
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["components"]["vector_detector"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Check reranker
        try:
            reranker_health = await self.reranker_service.health_check()
            health_status["components"]["reranker"] = reranker_health
            if reranker_health["status"] != "healthy":
                health_status["status"] = "degraded"
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["components"]["reranker"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        # Check cache
        try:
            cache_stats = await self.cache_service.get_stats()
            health_status["components"]["cache"] = {
                "status": "healthy",
                "stats": cache_stats
            }
        except Exception as e:
            health_status["status"] = "degraded"
            health_status["components"]["cache"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        return health_status
