"""
Vector-based intent detection implementation
"""

import time
from typing import List, Optional, Dict, Any
import structlog

from core.domain.entities import SearchCandidate
from core.domain.services import VectorStore, EmbeddingProvider
from core.domain.exceptions import VectorStoreError, EmbeddingError
from shared.types import QueryText, Result
from shared.utils.validation import validate_query

logger = structlog.get_logger(__name__)


class VectorBasedDetectorImpl:
    """
    Implementation of vector-based intent detection
    """
    
    def __init__(
        self,
        vector_store: VectorStore,
        embedding_provider: EmbeddingProvider,
        default_top_k: int = 5,
        similarity_threshold: float = 0.6
    ):
        self.vector_store = vector_store
        self.embedding_provider = embedding_provider
        self.default_top_k = default_top_k
        self.similarity_threshold = similarity_threshold
        
        logger.info(
            "Vector-based detector initialized",
            embedding_dimension=embedding_provider.get_embedding_dimension(),
            default_top_k=default_top_k,
            similarity_threshold=similarity_threshold
        )
    
    async def detect(
        self, 
        query: QueryText, 
        top_k: Optional[int] = None,
        filters: Optional[Dict[str, Any]] = None,
        custom_threshold: Optional[float] = None
    ) -> List[SearchCandidate]:
        """
        Detect intent using vector similarity search
        
        Args:
            query: Input query text
            top_k: Number of candidates to return
            filters: Optional metadata filters
            custom_threshold: Custom similarity threshold
            
        Returns:
            List of search candidates
        """
        start_time = time.time()
        
        # Validate input
        validate_query(query)
        
        top_k = top_k or self.default_top_k
        threshold = custom_threshold or self.similarity_threshold
        
        logger.debug(
            "Starting vector-based detection",
            query=query,
            top_k=top_k,
            threshold=threshold,
            filters=filters
        )
        
        try:
            # Generate embedding for query
            embedding_start = time.time()
            query_vector = await self.embedding_provider.embed_text(query)
            embedding_time = (time.time() - embedding_start) * 1000
            
            logger.debug(
                "Query embedding generated",
                embedding_dimension=len(query_vector),
                embedding_time_ms=embedding_time
            )
            
            # Search vector store
            search_start = time.time()
            candidates = await self.vector_store.search(
                query_vector=query_vector,
                top_k=top_k,
                filters=filters
            )
            search_time = (time.time() - search_start) * 1000
            
            # Filter by threshold
            filtered_candidates = [
                candidate for candidate in candidates
                if candidate.score >= threshold
            ]
            
            processing_time = (time.time() - start_time) * 1000
            
            logger.info(
                "Vector-based detection completed",
                total_candidates=len(candidates),
                filtered_candidates=len(filtered_candidates),
                embedding_time_ms=embedding_time,
                search_time_ms=search_time,
                total_time_ms=processing_time
            )
            
            if filtered_candidates:
                logger.debug(
                    "Top candidates",
                    candidates=[
                        {
                            "intent_id": c.intent_id,
                            "score": round(c.score, 3),
                            "text_preview": c.text[:50] + "..." if len(c.text) > 50 else c.text
                        }
                        for c in filtered_candidates[:3]
                    ]
                )
            
            return filtered_candidates
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(
                "Vector-based detection failed",
                error=str(e),
                error_type=type(e).__name__,
                processing_time_ms=processing_time
            )
            
            # Re-raise as domain exception
            if isinstance(e, (VectorStoreError, EmbeddingError)):
                raise
            else:
                raise VectorStoreError("search", str(e))
    
    async def get_similar_queries(
        self, 
        query: QueryText, 
        intent_id: Optional[str] = None,
        limit: int = 10
    ) -> List[SearchCandidate]:
        """
        Get similar queries for analysis
        
        Args:
            query: Input query
            intent_id: Optional intent filter
            limit: Maximum results
            
        Returns:
            List of similar queries
        """
        filters = {"intent_id": intent_id} if intent_id else None
        
        return await self.detect(
            query=query,
            top_k=limit,
            filters=filters,
            custom_threshold=0.3  # Lower threshold for analysis
        )
    
    async def batch_detect(
        self, 
        queries: List[QueryText],
        top_k: Optional[int] = None
    ) -> List[List[SearchCandidate]]:
        """
        Detect intents for multiple queries in batch
        
        Args:
            queries: List of queries
            top_k: Number of candidates per query
            
        Returns:
            List of candidate lists for each query
        """
        start_time = time.time()
        
        logger.info(
            "Starting batch vector detection",
            query_count=len(queries),
            top_k=top_k or self.default_top_k
        )
        
        try:
            # Generate embeddings in batch
            embedding_start = time.time()
            query_vectors = await self.embedding_provider.embed_batch(queries)
            embedding_time = (time.time() - embedding_start) * 1000
            
            # Search for each query
            all_results = []
            for i, (query, vector) in enumerate(zip(queries, query_vectors)):
                try:
                    candidates = await self.vector_store.search(
                        query_vector=vector,
                        top_k=top_k or self.default_top_k
                    )
                    
                    # Filter by threshold
                    filtered_candidates = [
                        candidate for candidate in candidates
                        if candidate.score >= self.similarity_threshold
                    ]
                    
                    all_results.append(filtered_candidates)
                    
                except Exception as e:
                    logger.warning(
                        "Failed to process query in batch",
                        query_index=i,
                        query=query,
                        error=str(e)
                    )
                    all_results.append([])
            
            processing_time = (time.time() - start_time) * 1000
            
            # Calculate statistics
            total_candidates = sum(len(results) for results in all_results)
            successful_queries = sum(1 for results in all_results if results)
            
            logger.info(
                "Batch vector detection completed",
                query_count=len(queries),
                successful_queries=successful_queries,
                total_candidates=total_candidates,
                embedding_time_ms=embedding_time,
                total_time_ms=processing_time,
                avg_time_per_query=processing_time / len(queries) if queries else 0
            )
            
            return all_results
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            
            logger.error(
                "Batch vector detection failed",
                query_count=len(queries),
                error=str(e),
                processing_time_ms=processing_time
            )
            
            raise VectorStoreError("batch_search", str(e))
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get vector store collection statistics"""
        try:
            return await self.vector_store.get_collection_info()
        except Exception as e:
            logger.error("Failed to get collection stats", error=str(e))
            raise VectorStoreError("get_stats", str(e))
    
    async def health_check(self) -> Dict[str, Any]:
        """Check health of vector-based detection"""
        health_status = {
            "status": "healthy",
            "checks": {},
            "timestamp": time.time()
        }
        
        try:
            # Test embedding generation
            test_embedding = await self.embedding_provider.embed_text("test query")
            health_status["checks"]["embedding"] = {
                "status": "healthy",
                "dimension": len(test_embedding)
            }
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["checks"]["embedding"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        try:
            # Test vector store
            collection_info = await self.vector_store.get_collection_info()
            health_status["checks"]["vector_store"] = {
                "status": "healthy",
                "info": collection_info
            }
        except Exception as e:
            health_status["status"] = "unhealthy"
            health_status["checks"]["vector_store"] = {
                "status": "unhealthy",
                "error": str(e)
            }
        
        return health_status
