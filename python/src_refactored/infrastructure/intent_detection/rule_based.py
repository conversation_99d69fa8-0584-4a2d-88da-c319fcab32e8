"""
Rule-based intent detection implementation
"""

import re
import time
from typing import List, Optional, Dict, Any
import structlog

from ...core.domain.entities import Rule<PERSON>atch, IntentRule
from ...core.domain.services import RuleBasedDetector
from ...core.domain.exceptions import RuleValidationError
from ...core.domain.value_objects import Match<PERSON><PERSON><PERSON>, ConfidenceScore
from ...shared.types import QueryText, IntentId
from ...shared.utils.text_processing import VietnameseTextProcessor

logger = structlog.get_logger(__name__)


class RuleBasedDetectorImpl(RuleBasedDetector):
    """
    Implementation of rule-based intent detection
    """
    
    def __init__(
        self,
        rules: List[IntentRule],
        text_processor: Optional[VietnameseTextProcessor] = None
    ):
        self.rules = self._validate_and_sort_rules(rules)
        self.text_processor = text_processor or VietnameseTextProcessor()
        self._compiled_patterns: Dict[IntentId, List[re.Pattern]] = {}
        self._compile_patterns()
        
        logger.info(
            "Rule-based detector initialized",
            rule_count=len(self.rules),
            intent_ids=[rule.intent_id for rule in self.rules]
        )
    
    def _validate_and_sort_rules(self, rules: List[IntentRule]) -> List[IntentRule]:
        """Validate and sort rules by weight (descending)"""
        if not rules:
            raise RuleValidationError("", "No rules provided")
        
        # Validate each rule
        for rule in rules:
            if not rule.enabled:
                continue
                
            if not rule.keywords and not rule.patterns:
                raise RuleValidationError(
                    rule.intent_id, 
                    "Rule must have at least one keyword or pattern"
                )
        
        # Sort by weight (descending) for priority processing
        active_rules = [rule for rule in rules if rule.enabled]
        return sorted(active_rules, key=lambda r: r.weight, reverse=True)
    
    def _compile_patterns(self) -> None:
        """Pre-compile regex patterns for better performance"""
        for rule in self.rules:
            compiled_patterns = []
            for pattern_str in rule.patterns:
                try:
                    pattern = re.compile(pattern_str, re.IGNORECASE)
                    compiled_patterns.append(pattern)
                except re.error as e:
                    logger.warning(
                        "Invalid regex pattern",
                        intent_id=rule.intent_id,
                        pattern=pattern_str,
                        error=str(e)
                    )
            
            self._compiled_patterns[rule.intent_id] = compiled_patterns
    
    async def detect(self, query: QueryText) -> Optional[RuleMatch]:
        """
        Detect intent using rule-based matching with optimized performance
        """
        start_time = time.time()
        
        # Normalize query
        normalized_query = self.text_processor.normalize_vietnamese(query)
        query_lower = normalized_query.lower()
        
        logger.debug(
            "Starting rule-based detection",
            original_query=query,
            normalized_query=normalized_query
        )
        
        all_matches: List[RuleMatch] = []
        
        # Process rules in priority order (highest weight first)
        for rule in self.rules:
            match = await self._match_rule(rule, query_lower, normalized_query)
            if match:
                all_matches.append(match)
                
                # Early exit for very high confidence matches
                if match.score >= 0.9 and rule.weight > 1.3:
                    logger.debug(
                        "Early exit - high confidence rule match",
                        intent_id=rule.intent_id,
                        score=match.score,
                        weight=rule.weight
                    )
                    processing_time = (time.time() - start_time) * 1000
                    logger.info(
                        "Rule-based detection completed (early exit)",
                        intent_id=match.intent_id,
                        confidence=match.score,
                        processing_time_ms=processing_time
                    )
                    return match
        
        processing_time = (time.time() - start_time) * 1000
        
        if not all_matches:
            logger.debug(
                "No rule matches found",
                processing_time_ms=processing_time
            )
            return None
        
        # Handle multiple matches - prioritize by position and score
        best_match = self._select_best_match(all_matches)
        
        logger.info(
            "Rule-based detection completed",
            intent_id=best_match.intent_id,
            confidence=best_match.score,
            total_matches=len(all_matches),
            processing_time_ms=processing_time
        )
        
        return best_match
    
    async def _match_rule(
        self, 
        rule: IntentRule, 
        query_lower: str, 
        normalized_query: str
    ) -> Optional[RuleMatch]:
        """Match a single rule against the query"""
        score = 0.0
        first_match_position = float('inf')
        matched_keywords: List[str] = []
        matched_patterns: List[str] = []
        
        # Keyword matching with early exit optimization
        keyword_found = False
        for keyword in rule.keywords:
            keyword_index = query_lower.find(keyword.lower())
            if keyword_index != -1:
                score += 0.4 * rule.weight
                first_match_position = min(first_match_position, keyword_index)
                matched_keywords.append(keyword)
                keyword_found = True
                
                # Early exit for high-weight rules with strong keyword match
                if rule.weight > 1.3 and score >= 0.8:
                    break
        
        # Pattern matching (only if keywords found or high-weight rule)
        if keyword_found or rule.weight > 1.2:
            compiled_patterns = self._compiled_patterns.get(rule.intent_id, [])
            for i, pattern in enumerate(compiled_patterns):
                match = pattern.search(query_lower)
                if match:
                    score += 0.6 * rule.weight
                    first_match_position = min(first_match_position, match.start())
                    matched_patterns.append(rule.patterns[i])
        
        if score > 0:
            # Normalize score to [0, 1] range
            final_score = min(score, 1.0)
            
            return RuleMatch(
                intent_id=rule.intent_id,
                matched_keywords=matched_keywords,
                matched_patterns=matched_patterns,
                score=final_score,
                weight=rule.weight,
                position=int(first_match_position) if first_match_position != float('inf') else 0
            )
        
        return None
    
    def _select_best_match(self, matches: List[RuleMatch]) -> RuleMatch:
        """Select the best match from multiple candidates"""
        if len(matches) == 1:
            return matches[0]
        
        # Sort by score first, then by position (earlier is better)
        def match_priority(match: RuleMatch) -> tuple:
            return (-match.score, match.position, -match.weight)
        
        sorted_matches = sorted(matches, key=match_priority)
        best_match = sorted_matches[0]
        
        # If the best match has low score, check if there's a better positioned match
        if best_match.score < 0.6:
            for match in sorted_matches[1:]:
                if match.score >= 0.5 and match.position < best_match.position:
                    return match
        
        return best_match
    
    async def get_rules(self) -> List[IntentRule]:
        """Get all active rules"""
        return self.rules.copy()
    
    async def add_rule(self, rule: IntentRule) -> None:
        """Add a new rule"""
        if not rule.enabled:
            logger.warning("Attempting to add disabled rule", intent_id=rule.intent_id)
            return
        
        # Validate rule
        if not rule.keywords and not rule.patterns:
            raise RuleValidationError(
                rule.intent_id,
                "Rule must have at least one keyword or pattern"
            )
        
        # Check for duplicate intent_id
        existing_rule = next((r for r in self.rules if r.intent_id == rule.intent_id), None)
        if existing_rule:
            raise RuleValidationError(
                rule.intent_id,
                f"Rule with intent_id '{rule.intent_id}' already exists"
            )
        
        # Add rule and re-sort
        self.rules.append(rule)
        self.rules.sort(key=lambda r: r.weight, reverse=True)
        
        # Compile patterns for new rule
        compiled_patterns = []
        for pattern_str in rule.patterns:
            try:
                pattern = re.compile(pattern_str, re.IGNORECASE)
                compiled_patterns.append(pattern)
            except re.error as e:
                logger.warning(
                    "Invalid regex pattern in new rule",
                    intent_id=rule.intent_id,
                    pattern=pattern_str,
                    error=str(e)
                )
        
        self._compiled_patterns[rule.intent_id] = compiled_patterns
        
        logger.info("Rule added successfully", intent_id=rule.intent_id, weight=rule.weight)
    
    async def update_rule(self, rule: IntentRule) -> None:
        """Update existing rule"""
        # Find and replace existing rule
        for i, existing_rule in enumerate(self.rules):
            if existing_rule.intent_id == rule.intent_id:
                self.rules[i] = rule
                break
        else:
            raise RuleValidationError(
                rule.intent_id,
                f"Rule with intent_id '{rule.intent_id}' not found"
            )
        
        # Re-sort rules
        self.rules.sort(key=lambda r: r.weight, reverse=True)
        
        # Re-compile patterns
        self._compile_patterns()
        
        logger.info("Rule updated successfully", intent_id=rule.intent_id)
    
    async def delete_rule(self, intent_id: IntentId) -> None:
        """Delete rule by intent ID"""
        original_count = len(self.rules)
        self.rules = [rule for rule in self.rules if rule.intent_id != intent_id]
        
        if len(self.rules) == original_count:
            raise RuleValidationError(
                intent_id,
                f"Rule with intent_id '{intent_id}' not found"
            )
        
        # Remove compiled patterns
        if intent_id in self._compiled_patterns:
            del self._compiled_patterns[intent_id]
        
        logger.info("Rule deleted successfully", intent_id=intent_id)
