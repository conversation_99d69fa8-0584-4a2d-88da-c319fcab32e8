"""
Configuration loader with environment variable support and validation
"""

import os
import json
from typing import Dict, Any, Optional, Type, TypeVar
from pathlib import Path
import structlog

from .settings import (
    AppConfig, Environment, VectorStoreType, EmbeddingProvider, 
    CacheBackend, LogLevel, ThresholdConfig, IntentDetectionConfig,
    VectorStoreConfig, EmbeddingConfig, CacheConfig, LoggingConfig
)
from ...core.domain.exceptions import ConfigurationError

logger = structlog.get_logger(__name__)

T = TypeVar('T')


class ConfigLoader:
    """
    Configuration loader with environment variable support
    """
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file
        self.env_prefix = "FPT_AGENT_"
    
    def load_config(self) -> AppConfig:
        """
        Load configuration from environment variables and config file
        
        Returns:
            Loaded and validated configuration
        """
        logger.info("Loading application configuration")
        
        # Start with default config
        config_dict = {}
        
        # Load from config file if provided
        if self.config_file and Path(self.config_file).exists():
            config_dict = self._load_from_file(self.config_file)
            logger.info("Configuration loaded from file", file=self.config_file)
        
        # Override with environment variables
        env_config = self._load_from_env()
        config_dict = self._deep_merge(config_dict, env_config)
        
        # Create configuration objects
        try:
            config = self._create_config(config_dict)
            config.validate()
            
            logger.info(
                "Configuration loaded successfully",
                environment=config.environment,
                debug=config.debug,
                vector_store_type=config.vector_store.type,
                embedding_provider=config.embedding.provider,
                cache_backend=config.cache.backend
            )
            
            return config
            
        except Exception as e:
            logger.error("Failed to load configuration", error=str(e))
            raise ConfigurationError("config_loading", str(e))
    
    def _load_from_file(self, config_file: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning("Configuration file not found", file=config_file)
            return {}
        except json.JSONDecodeError as e:
            raise ConfigurationError("config_file", f"Invalid JSON in config file: {e}")
        except Exception as e:
            raise ConfigurationError("config_file", f"Failed to read config file: {e}")
    
    def _load_from_env(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        config = {}
        
        # Main app settings
        config.update(self._load_app_env())
        
        # Intent detection settings
        intent_config = self._load_intent_detection_env()
        if intent_config:
            config["intent_detection"] = intent_config
        
        # Vector store settings
        vector_config = self._load_vector_store_env()
        if vector_config:
            config["vector_store"] = vector_config
        
        # Embedding settings
        embedding_config = self._load_embedding_env()
        if embedding_config:
            config["embedding"] = embedding_config
        
        # Cache settings
        cache_config = self._load_cache_env()
        if cache_config:
            config["cache"] = cache_config
        
        # Logging settings
        logging_config = self._load_logging_env()
        if logging_config:
            config["logging"] = logging_config
        
        return config
    
    def _load_app_env(self) -> Dict[str, Any]:
        """Load main app settings from environment"""
        config = {}
        
        # Environment
        if env_val := os.getenv(f"{self.env_prefix}ENVIRONMENT"):
            config["environment"] = env_val
        
        # Debug mode
        if env_val := os.getenv(f"{self.env_prefix}DEBUG"):
            config["debug"] = self._parse_bool(env_val)
        
        # API settings
        if env_val := os.getenv(f"{self.env_prefix}API_HOST"):
            config["api_host"] = env_val
        
        if env_val := os.getenv(f"{self.env_prefix}API_PORT"):
            config["api_port"] = self._parse_int(env_val)
        
        if env_val := os.getenv(f"{self.env_prefix}API_WORKERS"):
            config["api_workers"] = self._parse_int(env_val)
        
        if env_val := os.getenv(f"{self.env_prefix}API_KEY"):
            config["api_key"] = env_val
        
        # Data paths
        if env_val := os.getenv(f"{self.env_prefix}DATA_DIR"):
            config["data_dir"] = env_val
        
        return config
    
    def _load_intent_detection_env(self) -> Dict[str, Any]:
        """Load intent detection settings from environment"""
        config = {}
        prefix = f"{self.env_prefix}INTENT_"
        
        # Feature flags
        if env_val := os.getenv(f"{prefix}ENABLE_CACHE"):
            config["enable_cache"] = self._parse_bool(env_val)
        
        if env_val := os.getenv(f"{prefix}ENABLE_RULES"):
            config["enable_rules"] = self._parse_bool(env_val)
        
        if env_val := os.getenv(f"{prefix}ENABLE_VECTOR"):
            config["enable_vector"] = self._parse_bool(env_val)
        
        if env_val := os.getenv(f"{prefix}ENABLE_RERANKER"):
            config["enable_reranker"] = self._parse_bool(env_val)
        
        # Thresholds
        thresholds = {}
        if env_val := os.getenv(f"{prefix}THRESHOLD_VERY_HIGH"):
            thresholds["very_high"] = self._parse_float(env_val)
        
        if env_val := os.getenv(f"{prefix}THRESHOLD_HIGH"):
            thresholds["high"] = self._parse_float(env_val)
        
        if env_val := os.getenv(f"{prefix}THRESHOLD_MEDIUM"):
            thresholds["medium"] = self._parse_float(env_val)
        
        if env_val := os.getenv(f"{prefix}THRESHOLD_LOW"):
            thresholds["low"] = self._parse_float(env_val)
        
        if env_val := os.getenv(f"{prefix}THRESHOLD_RERANKER"):
            thresholds["reranker"] = self._parse_float(env_val)
        
        if thresholds:
            config["thresholds"] = thresholds
        
        # Performance settings
        if env_val := os.getenv(f"{prefix}MAX_CANDIDATES"):
            config["max_candidates"] = self._parse_int(env_val)
        
        if env_val := os.getenv(f"{prefix}VECTOR_TOP_K"):
            config["vector_top_k"] = self._parse_int(env_val)
        
        return config
    
    def _load_vector_store_env(self) -> Dict[str, Any]:
        """Load vector store settings from environment"""
        config = {}
        prefix = f"{self.env_prefix}VECTOR_"
        
        if env_val := os.getenv(f"{prefix}TYPE"):
            config["type"] = env_val
        
        if env_val := os.getenv(f"{prefix}HOST"):
            config["host"] = env_val
        
        if env_val := os.getenv(f"{prefix}PORT"):
            config["port"] = self._parse_int(env_val)
        
        if env_val := os.getenv(f"{prefix}COLLECTION_NAME"):
            config["collection_name"] = env_val
        
        if env_val := os.getenv(f"{prefix}TIMEOUT"):
            config["timeout"] = self._parse_float(env_val)
        
        # Qdrant specific
        if env_val := os.getenv("QDRANT_API_KEY"):
            config["qdrant_api_key"] = env_val
        
        if env_val := os.getenv("QDRANT_URL"):
            config["qdrant_url"] = env_val
        
        return config
    
    def _load_embedding_env(self) -> Dict[str, Any]:
        """Load embedding settings from environment"""
        config = {}
        prefix = f"{self.env_prefix}EMBEDDING_"
        
        if env_val := os.getenv(f"{prefix}PROVIDER"):
            config["provider"] = env_val
        
        # OpenAI settings
        if env_val := os.getenv("OPENAI_API_KEY"):
            config["openai_api_key"] = env_val
        
        if env_val := os.getenv(f"{prefix}OPENAI_MODEL"):
            config["openai_model"] = env_val
        
        if env_val := os.getenv(f"{prefix}OPENAI_DIMENSIONS"):
            config["openai_dimensions"] = self._parse_int(env_val)
        
        # Local model settings
        if env_val := os.getenv(f"{prefix}LOCAL_MODEL"):
            config["local_model_name"] = env_val
        
        if env_val := os.getenv(f"{prefix}LOCAL_DEVICE"):
            config["local_device"] = env_val
        
        return config
    
    def _load_cache_env(self) -> Dict[str, Any]:
        """Load cache settings from environment"""
        config = {}
        prefix = f"{self.env_prefix}CACHE_"
        
        if env_val := os.getenv(f"{prefix}BACKEND"):
            config["backend"] = env_val
        
        if env_val := os.getenv("REDIS_URL"):
            config["redis_url"] = env_val
        
        if env_val := os.getenv("REDIS_PASSWORD"):
            config["redis_password"] = env_val
        
        if env_val := os.getenv(f"{prefix}DEFAULT_TTL"):
            config["default_ttl"] = self._parse_int(env_val)
        
        if env_val := os.getenv(f"{prefix}MAX_TTL"):
            config["max_ttl"] = self._parse_int(env_val)
        
        return config
    
    def _load_logging_env(self) -> Dict[str, Any]:
        """Load logging settings from environment"""
        config = {}
        prefix = f"{self.env_prefix}LOG_"
        
        if env_val := os.getenv(f"{prefix}LEVEL"):
            config["level"] = env_val
        
        if env_val := os.getenv(f"{prefix}FORMAT"):
            config["format"] = env_val
        
        if env_val := os.getenv(f"{prefix}ENABLE_FILE"):
            config["enable_file_logging"] = self._parse_bool(env_val)
        
        if env_val := os.getenv(f"{prefix}FILE"):
            config["log_file"] = env_val
        
        return config
    
    def _create_config(self, config_dict: Dict[str, Any]) -> AppConfig:
        """Create AppConfig from dictionary"""
        # Create sub-configurations
        intent_config = IntentDetectionConfig()
        if "intent_detection" in config_dict:
            intent_config = self._update_dataclass(intent_config, config_dict["intent_detection"])
        
        vector_config = VectorStoreConfig()
        if "vector_store" in config_dict:
            vector_config = self._update_dataclass(vector_config, config_dict["vector_store"])
        
        embedding_config = EmbeddingConfig()
        if "embedding" in config_dict:
            embedding_config = self._update_dataclass(embedding_config, config_dict["embedding"])
        
        cache_config = CacheConfig()
        if "cache" in config_dict:
            cache_config = self._update_dataclass(cache_config, config_dict["cache"])
        
        logging_config = LoggingConfig()
        if "logging" in config_dict:
            logging_config = self._update_dataclass(logging_config, config_dict["logging"])
        
        # Create main config
        main_config = {k: v for k, v in config_dict.items() 
                      if k not in ["intent_detection", "vector_store", "embedding", "cache", "logging"]}
        
        app_config = self._update_dataclass(AppConfig(), main_config)
        
        # Set sub-configurations
        app_config.intent_detection = intent_config
        app_config.vector_store = vector_config
        app_config.embedding = embedding_config
        app_config.cache = cache_config
        app_config.logging = logging_config
        
        return app_config
    
    def _update_dataclass(self, instance: T, updates: Dict[str, Any]) -> T:
        """Update dataclass instance with dictionary values"""
        for key, value in updates.items():
            if hasattr(instance, key):
                # Handle nested dataclass (like thresholds)
                if key == "thresholds" and hasattr(instance, "thresholds"):
                    threshold_config = self._update_dataclass(instance.thresholds, value)
                    setattr(instance, key, threshold_config)
                else:
                    setattr(instance, key, value)
        return instance
    
    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """Deep merge two dictionaries"""
        result = dict1.copy()
        for key, value in dict2.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
        return result
    
    def _parse_bool(self, value: str) -> bool:
        """Parse boolean from string"""
        return value.lower() in ("true", "1", "yes", "on")
    
    def _parse_int(self, value: str) -> int:
        """Parse integer from string"""
        try:
            return int(value)
        except ValueError:
            raise ConfigurationError("parse_int", f"Invalid integer value: {value}")
    
    def _parse_float(self, value: str) -> float:
        """Parse float from string"""
        try:
            return float(value)
        except ValueError:
            raise ConfigurationError("parse_float", f"Invalid float value: {value}")


def load_config(config_file: Optional[str] = None) -> AppConfig:
    """
    Convenience function to load configuration
    
    Args:
        config_file: Optional path to configuration file
        
    Returns:
        Loaded configuration
    """
    loader = ConfigLoader(config_file)
    return loader.load_config()
