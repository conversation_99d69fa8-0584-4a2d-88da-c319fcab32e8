"""
Application configuration settings with type safety and validation
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
import os

from ...core.domain.exceptions import ConfigurationError
from ...shared.types import LogLevel


class Environment(str, Enum):
    """Application environment"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class VectorStoreType(str, Enum):
    """Vector store implementations"""
    QDRANT = "qdrant"
    MEMORY = "memory"
    CHROMA = "chroma"


class EmbeddingProvider(str, Enum):
    """Embedding providers"""
    OPENAI = "openai"
    LOCAL = "local"
    SENTENCE_TRANSFORMERS = "sentence_transformers"


class CacheBackend(str, Enum):
    """Cache backends"""
    REDIS = "redis"
    MEMORY = "memory"


@dataclass
class ThresholdConfig:
    """Confidence threshold configuration"""
    very_high: float = 0.9
    high: float = 0.7
    medium: float = 0.5
    low: float = 0.3
    reranker: float = 2.0
    
    def __post_init__(self):
        # Validate threshold values
        thresholds = [self.very_high, self.high, self.medium, self.low]
        
        for i, threshold in enumerate(thresholds):
            if not 0.0 <= threshold <= 1.0:
                raise ConfigurationError(
                    f"threshold_{i}", 
                    f"Threshold must be between 0.0 and 1.0, got {threshold}"
                )
        
        # Check ordering (should be descending)
        if not all(thresholds[i] >= thresholds[i+1] for i in range(len(thresholds)-1)):
            raise ConfigurationError(
                "thresholds",
                "Thresholds must be in descending order"
            )


@dataclass
class IntentDetectionConfig:
    """Intent detection service configuration"""
    enable_cache: bool = True
    enable_rules: bool = True
    enable_vector: bool = True
    enable_reranker: bool = True
    parallel_execution: bool = False
    
    # Thresholds
    thresholds: ThresholdConfig = field(default_factory=ThresholdConfig)
    
    # Performance settings
    early_exit_threshold: float = 0.9
    max_candidates: int = 5
    vector_top_k: int = 10
    
    # Timeouts (seconds)
    rule_timeout: float = 1.0
    vector_timeout: float = 5.0
    reranker_timeout: float = 3.0
    
    def __post_init__(self):
        if self.early_exit_threshold < 0.0 or self.early_exit_threshold > 1.0:
            raise ConfigurationError(
                "early_exit_threshold",
                f"Early exit threshold must be between 0.0 and 1.0, got {self.early_exit_threshold}"
            )
        
        if self.max_candidates <= 0:
            raise ConfigurationError(
                "max_candidates",
                f"Max candidates must be positive, got {self.max_candidates}"
            )


@dataclass
class VectorStoreConfig:
    """Vector store configuration"""
    type: VectorStoreType = VectorStoreType.QDRANT
    host: str = "localhost"
    port: int = 6333
    collection_name: str = "fpt_intents"
    
    # Connection settings
    timeout: float = 10.0
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # Performance settings
    batch_size: int = 100
    max_connections: int = 10
    
    # Qdrant specific
    qdrant_api_key: Optional[str] = None
    qdrant_url: Optional[str] = None
    
    def __post_init__(self):
        if self.port <= 0 or self.port > 65535:
            raise ConfigurationError(
                "port",
                f"Port must be between 1 and 65535, got {self.port}"
            )
        
        if self.timeout <= 0:
            raise ConfigurationError(
                "timeout",
                f"Timeout must be positive, got {self.timeout}"
            )
    
    @property
    def connection_url(self) -> str:
        """Get connection URL"""
        if self.qdrant_url:
            return self.qdrant_url
        return f"http://{self.host}:{self.port}"


@dataclass
class EmbeddingConfig:
    """Embedding provider configuration"""
    provider: EmbeddingProvider = EmbeddingProvider.OPENAI
    
    # OpenAI settings
    openai_api_key: Optional[str] = None
    openai_model: str = "text-embedding-3-small"
    openai_dimensions: int = 1536
    
    # Local model settings
    local_model_name: str = "sentence-transformers/all-MiniLM-L6-v2"
    local_device: str = "cpu"
    
    # Performance settings
    batch_size: int = 32
    max_retries: int = 3
    timeout: float = 30.0
    
    def __post_init__(self):
        if self.provider == EmbeddingProvider.OPENAI and not self.openai_api_key:
            # Try to get from environment
            self.openai_api_key = os.getenv("OPENAI_API_KEY")
            if not self.openai_api_key:
                raise ConfigurationError(
                    "openai_api_key",
                    "OpenAI API key is required when using OpenAI provider"
                )
        
        if self.openai_dimensions <= 0:
            raise ConfigurationError(
                "openai_dimensions",
                f"Embedding dimensions must be positive, got {self.openai_dimensions}"
            )


@dataclass
class CacheConfig:
    """Cache configuration"""
    backend: CacheBackend = CacheBackend.MEMORY
    
    # Redis settings
    redis_url: str = "redis://localhost:6379/0"
    redis_password: Optional[str] = None
    redis_db: int = 0
    
    # Memory cache settings
    memory_max_size: int = 1000
    
    # Common settings
    default_ttl: int = 300  # 5 minutes
    max_ttl: int = 3600     # 1 hour
    key_prefix: str = "fpt_agent:"
    
    # Performance settings
    connection_pool_size: int = 10
    timeout: float = 5.0
    
    def __post_init__(self):
        if self.default_ttl <= 0:
            raise ConfigurationError(
                "default_ttl",
                f"Default TTL must be positive, got {self.default_ttl}"
            )
        
        if self.max_ttl < self.default_ttl:
            raise ConfigurationError(
                "max_ttl",
                f"Max TTL must be >= default TTL, got {self.max_ttl} < {self.default_ttl}"
            )


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: LogLevel = LogLevel.INFO
    format: str = "json"  # json or text
    
    # File logging
    enable_file_logging: bool = False
    log_file: str = "logs/app.log"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    
    # Structured logging
    include_timestamp: bool = True
    include_level: bool = True
    include_logger_name: bool = True
    include_function_name: bool = False
    include_line_number: bool = False
    
    # Performance logging
    log_slow_queries: bool = True
    slow_query_threshold: float = 1000.0  # milliseconds
    
    # External services
    log_external_requests: bool = True
    mask_sensitive_data: bool = True
    
    def __post_init__(self):
        if self.max_file_size <= 0:
            raise ConfigurationError(
                "max_file_size",
                f"Max file size must be positive, got {self.max_file_size}"
            )
        
        if self.backup_count < 0:
            raise ConfigurationError(
                "backup_count",
                f"Backup count must be non-negative, got {self.backup_count}"
            )


@dataclass
class AppConfig:
    """Main application configuration"""
    # Environment
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    
    # Service configurations
    intent_detection: IntentDetectionConfig = field(default_factory=IntentDetectionConfig)
    vector_store: VectorStoreConfig = field(default_factory=VectorStoreConfig)
    embedding: EmbeddingConfig = field(default_factory=EmbeddingConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    # API settings
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 1
    
    # Security
    api_key: Optional[str] = None
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    
    # Monitoring
    enable_metrics: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 30
    
    # Data paths
    data_dir: str = "data"
    rules_file: str = "intent_rules.json"
    examples_file: str = "intent-examples.json"
    
    def __post_init__(self):
        # Validate API settings
        if self.api_port <= 0 or self.api_port > 65535:
            raise ConfigurationError(
                "api_port",
                f"API port must be between 1 and 65535, got {self.api_port}"
            )
        
        if self.api_workers <= 0:
            raise ConfigurationError(
                "api_workers",
                f"API workers must be positive, got {self.api_workers}"
            )
        
        # Set debug mode based on environment
        if self.environment == Environment.DEVELOPMENT:
            self.debug = True
        
        # Adjust logging level based on environment
        if self.environment == Environment.PRODUCTION:
            if self.logging.level == LogLevel.DEBUG:
                self.logging.level = LogLevel.INFO
    
    def is_production(self) -> bool:
        """Check if running in production"""
        return self.environment == Environment.PRODUCTION
    
    def is_development(self) -> bool:
        """Check if running in development"""
        return self.environment == Environment.DEVELOPMENT
    
    def get_data_path(self, filename: str) -> str:
        """Get full path for data file"""
        return os.path.join(self.data_dir, filename)
    
    def validate(self) -> None:
        """Validate entire configuration"""
        # This method can be extended to add cross-field validation
        pass
