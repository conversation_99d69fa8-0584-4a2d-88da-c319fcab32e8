"""
Demo script for the refactored intent detection system
"""

import asyncio
import time
from typing import List, Dict, Any
import structlog

# Import refactored components
from infrastructure.config.loader import load_config
from infrastructure.logging.setup import setup_logging
from core.domain.entities import DetectionContext, IntentRule
from core.domain.exceptions import IntentDetectionError
from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
from infrastructure.intent_detection.reranker import RerankerServiceImpl
from infrastructure.intent_detection.hybrid import HybridIntentDetectionService
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl
from infrastructure.caching.memory_cache import MemoryCacheService
from infrastructure.vector_stores.memory_store import MemoryVectorStore
from infrastructure.embeddings.openai_embeddings import OpenAIEmbeddingProvider

logger = structlog.get_logger(__name__)


# Sample intent rules for demo
DEMO_RULES = [
    IntentRule(
        intent_id="tuition_inquiry",
        keywords=[
            'học phí', 'tuition', 'phí', 'tiền', 'cost', 'chi phí', 'trả góp',
            'thanh toán', 'ojt', 'fee', 'mắc', 'đắt', 'rẻ', 'expensive', 'cheap'
        ],
        patterns=[
            r"học phí.*(?:bao nhiêu|đắt|rẻ|giá|cost|mắc)",
            r"(?:chi phí|tiền|phí).*(?:học|ngành|năm|semester|kỳ)",
            r"tuition.*(?:fee|của|bao nhiêu|FPT|program|IT|AI)",
        ],
        weight=1.4,
        description="Tuition and fee inquiries"
    ),
    IntentRule(
        intent_id="campus_info",
        keywords=[
            'campus', 'cơ sở', 'thư viện', 'library', 'ký túc xá', 'dormitory',
            'cơ sở vật chất', 'facilities', 'địa chỉ', 'address', 'location'
        ],
        patterns=[
            r"(?:campus|cơ sở).*(?:ở đâu|where|địa chỉ|location)",
            r"thư viện.*(?:mở cửa|giờ|hours|time)",
            r"ký túc xá.*(?:giá|price|cost|bao nhiêu)",
        ],
        weight=1.2,
        description="Campus and facilities information"
    ),
    IntentRule(
        intent_id="program_requirements",
        keywords=[
            'điểm chuẩn', 'admission', 'requirements', 'yêu cầu', 'đầu vào',
            'tuyển sinh', 'enrollment', 'điều kiện', 'conditions'
        ],
        patterns=[
            r"điểm chuẩn.*(?:bao nhiêu|2024|2025|năm)",
            r"(?:yêu cầu|requirements).*(?:đầu vào|admission|tuyển sinh)",
            r"(?:điều kiện|conditions).*(?:học|study|program)",
        ],
        weight=1.3,
        description="Program admission requirements"
    )
]

# Sample queries for testing
TEST_QUERIES = [
    "Học phí FPT 2025 bao nhiêu tiền?",
    "Thư viện mở cửa lúc mấy giờ?",
    "Điểm chuẩn FPT 2024 bao nhiêu?",
    "Campus FPT ở đâu?",
    "Yêu cầu đầu vào ngành IT như thế nào?",
    "Ký túc xá FPT giá bao nhiêu một tháng?",
    "Tuition fee for AI program?",
    "What are the admission requirements?",
    "Hôm nay trời có mưa không?",  # Irrelevant query
    "Cách nấu phở bò Hà Nội?"      # Irrelevant query
]


class DemoVectorStore(MemoryVectorStore):
    """Demo vector store with sample data"""
    
    def __init__(self):
        super().__init__()
        # Add some sample documents
        self._add_sample_documents()
    
    def _add_sample_documents(self):
        """Add sample documents for demo"""
        sample_docs = [
            {
                "text": "Học phí ngành Công nghệ thông tin năm 2025 là 25 triệu đồng",
                "intent_id": "tuition_inquiry",
                "metadata": {"year": "2025", "program": "IT"}
            },
            {
                "text": "Thư viện FPT mở cửa từ 7:00 đến 22:00 hàng ngày",
                "intent_id": "campus_info", 
                "metadata": {"facility": "library", "hours": "7:00-22:00"}
            },
            {
                "text": "Điểm chuẩn FPT 2024 là 22 điểm cho ngành CNTT",
                "intent_id": "program_requirements",
                "metadata": {"year": "2024", "program": "IT", "score": 22}
            }
        ]
        
        # Simulate adding documents with dummy vectors
        for i, doc in enumerate(sample_docs):
            # Create dummy embedding vector
            vector = [0.1 * (i + 1)] * 1536  # OpenAI embedding dimension
            
            self.documents[str(i)] = {
                "id": str(i),
                "vector": vector,
                "text": doc["text"],
                "metadata": {
                    "intent_id": doc["intent_id"],
                    **doc["metadata"]
                }
            }


class DemoEmbeddingProvider(OpenAIEmbeddingProvider):
    """Demo embedding provider that returns dummy vectors"""
    
    def __init__(self):
        # Don't call super().__init__() to avoid requiring API key
        self.model_name = "demo-model"
        self.dimensions = 1536
    
    async def embed_text(self, text: str) -> List[float]:
        """Return dummy embedding vector"""
        # Simple hash-based dummy embedding
        hash_value = hash(text) % 1000
        return [hash_value / 1000.0] * self.dimensions
    
    async def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """Return dummy embedding vectors for batch"""
        return [await self.embed_text(text) for text in texts]
    
    def get_embedding_dimension(self) -> int:
        return self.dimensions
    
    def get_model_info(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "dimensions": self.dimensions,
            "provider": "demo"
        }


async def run_demo():
    """Run the intent detection demo"""
    
    print("🌸 FPT University Agent - Refactored Demo")
    print("=" * 50)
    
    # Load configuration
    print("📋 Loading configuration...")
    config = load_config()
    
    # Setup logging
    setup_logging(config.logging, config.environment)
    logger.info("Demo started", version="0.2.0")
    
    # Initialize components
    print("🔧 Initializing components...")
    
    # Text processor
    text_processor = VietnameseTextProcessor()
    
    # Metrics collector
    metrics_collector = MetricsCollectorImpl()
    
    # Rule-based detector
    rule_detector = RuleBasedDetectorImpl(
        rules=DEMO_RULES,
        text_processor=text_processor
    )
    
    # Vector store (demo)
    vector_store = DemoVectorStore()
    
    # Embedding provider (demo)
    embedding_provider = DemoEmbeddingProvider()
    
    # Reranker service
    reranker_service = RerankerServiceImpl(
        model_name='cross-encoder/ms-marco-MiniLM-L-6-v2',
        score_threshold=2.0
    )
    
    # Cache service
    cache_service = MemoryCacheService(max_size=1000)
    
    # Hybrid intent detection service
    intent_service = HybridIntentDetectionService(
        config=config,
        rule_detector=rule_detector,
        vector_store=vector_store,
        embedding_provider=embedding_provider,
        reranker_service=reranker_service,
        cache_service=cache_service,
        text_processor=text_processor,
        metrics_collector=metrics_collector
    )
    
    print("✅ Components initialized successfully!")
    print()
    
    # Health check
    print("❤️ Performing health check...")
    health_status = await intent_service.health_check()
    print(f"Service status: {health_status['status']}")
    
    for component, status in health_status['components'].items():
        status_icon = "✅" if status['status'] == 'healthy' else "⚠️"
        print(f"  {status_icon} {component}: {status['status']}")
    
    print()
    
    # Test queries
    print("🎯 Testing intent detection...")
    print("-" * 30)
    
    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n{i}. Query: '{query}'")
        
        # Create detection context
        context = DetectionContext(
            query=query,
            user_id="demo_user",
            session_id="demo_session",
            language="vi" if any(ord(c) > 127 for c in query) else "en"
        )
        
        # Detect intent
        start_time = time.time()
        result = await intent_service.detect(context)
        duration_ms = (time.time() - start_time) * 1000
        
        if result.is_ok():
            intent_result = result.data
            confidence_icon = "🟢" if intent_result.confidence >= 0.7 else "🟡" if intent_result.confidence >= 0.5 else "🔴"
            
            print(f"   {confidence_icon} Intent: {intent_result.id}")
            print(f"   📊 Confidence: {intent_result.confidence:.3f}")
            print(f"   🔧 Method: {intent_result.method}")
            print(f"   ⏱️ Duration: {duration_ms:.1f}ms")
            
            if intent_result.metadata:
                print(f"   📝 Metadata: {intent_result.metadata}")
        else:
            print(f"   ❌ Error: {result.error}")
        
        # Small delay for demo effect
        await asyncio.sleep(0.1)
    
    print()
    
    # Performance metrics
    print("📊 Performance Metrics:")
    print("-" * 20)
    
    metrics = await intent_service.get_performance_metrics()
    
    # Display key metrics
    counters = metrics.get('counters', {})
    histograms = metrics.get('histograms', {})
    
    print(f"Total requests: {counters.get('intent_detection_requests', 0)}")
    print(f"Cache hits: {counters.get('cache_hits', 0)}")
    print(f"Cache misses: {counters.get('cache_misses', 0)}")
    print(f"Successful detections: {counters.get('intent_detection_success', 0)}")
    print(f"Errors: {counters.get('intent_detection_errors', 0)}")
    
    if 'intent_detection_duration' in histograms:
        duration_stats = histograms['intent_detection_duration']
        print(f"Average response time: {duration_stats.get('avg', 0):.1f}ms")
        print(f"P95 response time: {duration_stats.get('p95', 0):.1f}ms")
    
    print()
    
    # Batch testing
    print("🚀 Testing batch detection...")
    batch_queries = TEST_QUERIES[:5]  # First 5 queries
    batch_contexts = [
        DetectionContext(query=query, user_id="batch_user")
        for query in batch_queries
    ]
    
    start_time = time.time()
    batch_results = await intent_service.detect_batch(batch_contexts)
    batch_duration = (time.time() - start_time) * 1000
    
    successful_batch = sum(1 for r in batch_results if r.is_ok())
    print(f"Batch size: {len(batch_queries)}")
    print(f"Successful: {successful_batch}/{len(batch_queries)}")
    print(f"Total time: {batch_duration:.1f}ms")
    print(f"Avg per query: {batch_duration/len(batch_queries):.1f}ms")
    
    print()
    print("🎉 Demo completed successfully!")
    
    # Final metrics report
    final_metrics = await intent_service.get_performance_metrics()
    logger.info("Demo completed", final_metrics=final_metrics)


if __name__ == "__main__":
    try:
        asyncio.run(run_demo())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
