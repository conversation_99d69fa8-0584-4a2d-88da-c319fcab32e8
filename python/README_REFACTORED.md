# 🏗️ **FPT University Agent - Refactored Architecture**

## 📁 **<PERSON><PERSON><PERSON> trúc thư mục mới (Clean Architecture)**

```
python/src_refactored/
├── core/                           # 🧠 Core Business Logic
│   ├── domain/                     # 📦 Domain Layer
│   │   ├── entities.py            # 🎯 Business entities
│   │   ├── value_objects.py       # 💎 Value objects
│   │   ├── exceptions.py          # ❌ Domain exceptions
│   │   └── services.py            # 🔧 Domain services (interfaces)
│   └── application/                # 🎮 Application Layer
│       ├── use_cases/             # 📋 Use cases
│       │   ├── detect_intent.py   # 🎯 Intent detection use case
│       │   └── batch_detect.py    # 📊 Batch detection use case
│       ├── services/              # 🔧 Application services
│       │   ├── intent_service.py  # 🎯 Intent application service
│       │   └── cache_service.py   # 💾 Cache application service
│       └── dto/                   # 📄 Data transfer objects
│           ├── requests.py        # 📥 Request DTOs
│           └── responses.py       # 📤 Response DTOs
├── infrastructure/                 # 🏗️ Infrastructure Layer
│   ├── intent_detection/          # 🎯 Intent detection implementations
│   │   ├── rule_based.py         # 📋 Rule-based detector
│   │   ├── vector_based.py       # 🔍 Vector-based detector
│   │   ├── reranker.py           # 🔄 Reranking service
│   │   └── hybrid.py             # 🔀 Hybrid orchestrator
│   ├── vector_stores/             # 💾 Vector database implementations
│   │   ├── qdrant_store.py       # 🗄️ Qdrant implementation
│   │   └── memory_store.py       # 🧠 In-memory implementation
│   ├── embeddings/                # 🧠 Embedding providers
│   │   ├── openai_embeddings.py  # 🤖 OpenAI implementation
│   │   └── local_embeddings.py   # 🏠 Local model implementation
│   ├── caching/                   # 💾 Caching implementations
│   │   ├── redis_cache.py        # 🔴 Redis implementation
│   │   └── memory_cache.py       # 🧠 In-memory implementation
│   ├── config/                    # ⚙️ Configuration
│   │   ├── settings.py           # 📋 Application settings
│   │   └── loader.py             # 📥 Configuration loader
│   └── logging/                   # 📝 Logging setup
│       ├── setup.py              # 🔧 Logging configuration
│       └── formatters.py         # 🎨 Custom formatters
├── presentation/                   # 🎨 Presentation Layer
│   ├── api/                       # 🌐 REST API
│   │   ├── routes/               # 🛣️ API routes
│   │   │   ├── intent.py         # 🎯 Intent endpoints
│   │   │   └── health.py         # ❤️ Health check
│   │   ├── middleware/           # 🔧 API middleware
│   │   │   ├── auth.py           # 🔐 Authentication
│   │   │   ├── logging.py        # 📝 Request logging
│   │   │   └── error_handler.py  # ❌ Error handling
│   │   └── schemas/              # 📋 API schemas
│   │       ├── requests.py       # 📥 Request schemas
│   │       └── responses.py      # 📤 Response schemas
│   └── cli/                       # 💻 Command line interface
│       ├── commands/             # 📋 CLI commands
│       │   ├── detect.py         # 🎯 Detection command
│       │   └── benchmark.py      # 📊 Benchmark command
│       └── main.py               # 🚀 CLI entry point
├── shared/                        # 🤝 Shared utilities
│   ├── utils/                    # 🔧 Utility functions
│   │   ├── text_processing.py   # 📝 Text utilities
│   │   ├── validation.py        # ✅ Validation helpers
│   │   └── metrics.py           # 📊 Metrics utilities
│   ├── constants.py              # 📋 Application constants
│   └── types.py                  # 🏷️ Common type definitions
└── tests/                        # 🧪 Test suites
    ├── unit/                     # 🔬 Unit tests
    │   ├── core/                # 🧠 Core tests
    │   ├── infrastructure/      # 🏗️ Infrastructure tests
    │   └── presentation/        # 🎨 Presentation tests
    ├── integration/              # 🔗 Integration tests
    │   ├── api/                 # 🌐 API tests
    │   └── services/            # 🔧 Service tests
    ├── performance/              # ⚡ Performance tests
    │   ├── load_tests.py        # 📊 Load testing
    │   └── benchmark.py         # 🏃 Benchmarking
    ├── fixtures/                 # 📦 Test fixtures
    │   ├── data/                # 📄 Test data
    │   └── mocks/               # 🎭 Mock objects
    └── conftest.py              # ⚙️ Pytest configuration
```

## 🎯 **Nguyên tắc thiết kế**

### **1. Clean Architecture**
- **Domain Layer**: Business logic thuần túy, không dependency
- **Application Layer**: Use cases và orchestration
- **Infrastructure Layer**: External dependencies (DB, API, etc.)
- **Presentation Layer**: User interfaces (API, CLI)

### **2. Dependency Injection**
- Interfaces trong core, implementations trong infrastructure
- Constructor injection với type hints
- Easy testing với mock objects

### **3. Async/Await First**
- Tất cả I/O operations đều async
- Connection pooling và resource management
- Concurrent processing cho better performance

### **4. Type Safety**
- Comprehensive type hints
- Pydantic models cho validation
- mypy compliance

### **5. Error Handling**
- Custom exception hierarchy
- Structured logging với context
- Graceful degradation

## 🚀 **Migration Plan**

1. **Phase 1**: Core domain entities và interfaces
2. **Phase 2**: Infrastructure implementations
3. **Phase 3**: Application services và use cases  
4. **Phase 4**: Presentation layer (API + CLI)
5. **Phase 5**: Testing và documentation
6. **Phase 6**: Performance optimization
7. **Phase 7**: Production deployment

## 📊 **Benefits của refactoring**

- **🔧 Maintainability**: Modular, single responsibility
- **🧪 Testability**: Easy mocking và unit testing
- **⚡ Performance**: Async operations, caching
- **🔒 Type Safety**: Comprehensive type checking
- **📈 Scalability**: Easy to extend và modify
- **🐛 Debugging**: Better error handling và logging
- **👥 Team Collaboration**: Clear separation of concerns
