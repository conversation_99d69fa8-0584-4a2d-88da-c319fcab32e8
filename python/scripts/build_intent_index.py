import os
import json
import uuid
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models
from llama_index.core import Document
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.settings import Settings

# --- C<PERSON>u hình ---
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

COLLECTION_NAME = "intent_examples_python_hybrid"
DATA_FILE_PATH = os.path.join(
    os.path.dirname(__file__), '..', 'data', 'intent-examples-augmented.json'
)
EMBEDDING_MODEL = "text-embedding-3-small"
EMBEDDING_DIM = 1536  # Kích thước vector của text-embedding-3-small

# --- Khởi tạo ---
qdrant_client = QdrantClient(
    host=os.getenv("QDRANT_HOST", "localhost"),
    port=int(os.getenv("QDRANT_PORT", 6333)),
    # api_key=os.getenv("QDRANT_API_KEY") # Bỏ comment nếu có API key
)

Settings.embed_model = OpenAIEmbedding(
    model=EMBEDDING_MODEL,
    api_key=os.getenv("OPENAI_API_KEY"),
    api_base=os.getenv("OPENAI_BASE_URL")
)


def load_documents_from_intents(file_path):
    """Tải các document từ file intent-examples.json."""
    documents = []
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    for intent in data.get("intents", []):
        intent_id = intent.get("id")
        routing = intent.get("routing")
        tools = intent.get("tools")

        for example in intent.get("examples", []):
            doc = Document(text=example, metadata={
                "intent_id": intent_id,
                "routing": routing,
                "tools": tools,
            })
            documents.append(doc)
    return documents


def create_qdrant_collection_if_not_exists():
    """Kiểm tra và tạo collection trong Qdrant nếu chưa có."""
    try:
        collections = qdrant_client.get_collections().collections
        collection_names = [col.name for col in collections]

        if COLLECTION_NAME in collection_names:
            print(
                f"ℹ️ Collection '{COLLECTION_NAME}' đã tồn tại. Sẽ xoá và tạo lại.")
            qdrant_client.delete_collection(collection_name=COLLECTION_NAME)

        print(f"🔨 Tạo collection mới: '{COLLECTION_NAME}'")
        qdrant_client.create_collection(
            collection_name=COLLECTION_NAME,
            vectors_config=models.VectorParams(
                size=EMBEDDING_DIM,
                distance=models.Distance.COSINE
            )
        )
        print("✅ Collection đã được tạo thành công.")

    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra/tạo collection: {e}")
        raise


def main():
    """Hàm chính để thực thi việc indexing."""
    print("--- Bắt đầu quá trình Indexing dữ liệu vào Qdrant ---")

    # 1. Tạo collection
    try:
        create_qdrant_collection_if_not_exists()
    except Exception:
        return  # Dừng lại nếu không tạo được collection

    # 2. Tải documents
    documents = load_documents_from_intents(DATA_FILE_PATH)
    if not documents:
        print("❌ Không tìm thấy document nào để index.")
        return
    print(f"📚 Đã tải {len(documents)} documents.")

    # 3. Tạo embeddings và chuẩn bị points cho Qdrant
    points_to_upsert = []
    texts_to_embed = [doc.text for doc in documents]

    print("🧠 Bắt đầu tạo embeddings...")
    embeddings = Settings.embed_model.get_text_embedding_batch(
        texts_to_embed, show_progress=True)
    print("✅ Đã tạo embeddings thành công.")

    for i, doc in enumerate(documents):
        # Đưa cả text và metadata vào payload
        payload = doc.metadata.copy()
        payload['text'] = doc.text

        point = models.PointStruct(
            id=str(uuid.uuid4()),
            vector=embeddings[i],
            payload=payload
        )
        points_to_upsert.append(point)

    # 4. Upsert (thêm/cập nhật) points vào Qdrant theo từng batch
    if not points_to_upsert:
        print("⚠️ Không có points nào để upsert.")
        return

    batch_size = 512
    print(
        f"🚀 Chuẩn bị upsert {len(points_to_upsert)} points vào Qdrant với batch size là {batch_size}...")

    try:
        # Chia list points thành các batch nhỏ hơn
        for i in range(0, len(points_to_upsert), batch_size):
            batch = points_to_upsert[i:i + batch_size]
            print(
                f"  - Đang upsert batch {i//batch_size + 1} (gồm {len(batch)} points)...")
            qdrant_client.upsert(
                collection_name=COLLECTION_NAME,
                points=batch,
                wait=True
            )

        print("✅ Upsert toàn bộ dữ liệu thành công!")

        # 5. Kiểm tra lại số lượng points trong collection
        collection_info = qdrant_client.get_collection(
            collection_name=COLLECTION_NAME)
        print(
            f"📊 Kiểm tra: Collection '{COLLECTION_NAME}' hiện có {collection_info.points_count} points.")

    except Exception as e:
        print(f"❌ Lỗi khi upsert dữ liệu vào Qdrant: {e}")

    print("--- Hoàn tất quá trình Indexing ---")


if __name__ == "__main__":
    main()
