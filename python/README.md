# FPT University Agent System - Python

> <PERSON>ệ thống Agent cho FPT University được viết lại bằng Python với [Agno framework](https://github.com/agno-agi/agno)

## 🚀 Tính năng

- **Intent Detection**: Ph<PERSON>t hiện ý định của sinh viên từ câu hỏi
- **Multi-Agent System**: <PERSON><PERSON> thống nhiều agent chuyên biệt 
- **Vietnamese Support**: Hỗ trợ tiếng Việt native
- **Agno Framework**: Sử dụng framework hiện đại cho AI agents

## 🛠️ Cài đặt

```bash
# Install dependencies
poetry install

# Activate virtual environment
poetry shell

# Set up environment variables
cp env_example.txt .env
# Edit .env với API keys của bạn
```

## 🔧 Cấu hình

Tạo file `.env` từ template:

```bash
cp env_example.txt .env
# Edit .env với API keys của bạn
```

## 🎯 Sử dụng

### Demo Intent Detection

```bash
python main.py
```

### Tạo Agent cơ bản

```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat

agent = Agent(
    name="FPT Support Agent",
    role="Hỗ trợ sinh viên FPT University",
    model=OpenAIChat(id="gpt-4"),
    instructions=[
        "Trả lời bằng tiếng Việt",
        "Cung cấp thông tin chính xác về FPT University",
        "Thân thiện và chuyên nghiệp"
    ]
)

response = agent.run("Học phí FPT 2025 bao nhiêu?")
print(response.content)
```

### Chạy với Docker Compose (Khuyến nghị)

Cách này sẽ tự động khởi tạo service ChromaDB và ứng dụng của bạn.

```bash
# Đảm bảo bạn đã cài đặt Docker và Docker Compose

# Build và khởi chạy các service ở chế độ detached
docker-compose up --build -d

# Xem logs của ứng dụng
docker-compose logs -f app

# Dừng các service
docker-compose down
```

### Chạy Indexing Script

Để "dạy" cho hệ thống, bạn cần chạy script để indexing dữ liệu vào ChromaDB.

```bash
# Chạy script indexing bên trong container của app
docker-compose exec app poetry run python scripts/index_data.py
```

## 📁 Cấu trúc project

```
python/
├── src/                 # Source code (để research dần)
├── main.py              # 🚀 Demo script
├── env_example.txt      # 🔐 Environment template
├── pyproject.toml       # ⚙️ Poetry configuration
├── poetry.lock          # 🔒 Locked dependencies
└── README.md           # 📖 This file
```

## 🧪 Development

### Chạy local (Không dùng Docker)
```bash
# Activate environment
poetry env activate

# Run demo
python main.py

# Add new dependencies
poetry add package-name

# Run tests (TODO)
pytest tests/
```

## 📚 Tài liệu

- [Agno Documentation](https://docs.agno.com/)
- [OpenAI API](https://platform.openai.com/docs)
- [Poetry Documentation](https://python-poetry.org/docs/)

## 🤝 So sánh với TypeScript version

| Feature | TypeScript | Python |
|---------|------------|---------|
| Performance | ⚡ Faster | 🐍 Slower |
| ML Ecosystem | 🔧 Limited | 🧠 Rich |
| Development Speed | 📝 Medium | 🚀 Fast |
| Type Safety | ✅ Strong | ⚠️ Optional |
| Vietnamese NLP | 🔧 Manual | 📚 Libraries |
| Package Management | 📦 npm/bun | 🎯 poetry |

## 🎯 Research Roadmap

- [ ] **Intent Detection** với Agno agents
- [ ] **Vector Database** research (ChromaDB, FAISS)  
- [ ] **Vietnamese NLP** tools (underthesea)
- [ ] **Multi-Agent Teams** implementation
- [ ] **Performance comparison** vs TypeScript version
- [ ] **RAG** implementation nếu cần
- [ ] **Production** setup khi ready

## 🐞 Issues & Development

Project này đang trong giai đoạn research và development. Mọi góp ý xin gửi về team phát triển! 