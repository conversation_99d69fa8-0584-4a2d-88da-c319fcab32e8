"""
Intent rules for FPT University Agent
"""
import re

# Tách riêng phần định nghĩa rule để tránh lỗi
INTENT_RULES = [
    {
        "intentId": "tuition_inquiry",
        "matchers": {
            "keywords": [
                'học phí', 'tuition', 'phí', 'tiền', 'cost', 'chi phí', 'trả góp',
                'thanh toán', 'ojt', 'fee', 'mắc', 'đắt', 'rẻ', 'expensive', 'cheap'
            ],
            "patterns": [
                re.compile(
                    r"học phí.*(?:bao nhiêu|đắt|rẻ|giá|cost|mắc)", re.IGNORECASE),
                re.compile(
                    r"(?:chi phí|tiền|phí).*(?:học|ngành|năm|semester|kỳ)", re.IGNORECASE),
                re.compile(
                    r"tuition.*(?:fee|của|bao nhiêu|FPT|program|IT|AI)", re.IGNORECASE),
            ],
            "weight": 1.4
        }
    },
    {
        "intentId": "campus_info",
        "matchers": {
            "keywords": [
                'campus', 'thư viện', 'library', 'lab', 'wifi', 'gym',
                'ký túc xá', 'gpu', 'rtx', 'cuda', 'facilities'
            ],
            "patterns": [
                re.compile(r"thư viện.*(?:mở|đóng|giờ|hoạt động)",
                           re.IGNORECASE),
                re.compile(
                    r"campus.*(?:hà nội|hcm|đà nẵng|cần thơ|quy nhon|hòa lạc)", re.IGNORECASE),
                re.compile(
                    r"(?:wifi|mạng).*(?:campus|trường|mạnh|yếu)", re.IGNORECASE),
            ],
            "weight": 1.2
        }
    },
    {
        "intentId": "program_requirements",
        "matchers": {
            "keywords": [
                'điểm chuẩn', 'điều kiện', 'yêu cầu', 'ielts', 'toefl',
                'portfolio', 'xét tuyển', 'requirement', 'khó vào'
            ],
            "patterns": [
                re.compile(r"điểm chuẩn.*(?:fpt|năm|bao nhiêu)",
                           re.IGNORECASE),
                re.compile(
                    r"(?:điều kiện|requirement).*(?:vào|nhập học)", re.IGNORECASE),
                re.compile(
                    r"(?:ielts|toefl|toeic).*(?:bao nhiêu|yêu cầu|bắt buộc|point)", re.IGNORECASE),
            ],
            "weight": 1.25
        }
    },
    {
        "intentId": "program_search",
        "matchers": {
            "keywords": [
                'ngành', 'major', 'program', 'khóa học', 'curriculum', 'chương trình'
            ],
            "patterns": [
                re.compile(
                    r"(?:ngành|major).*(?:gì|nào|hot|tốt|dễ)", re.IGNORECASE),
                re.compile(
                    r"(?:ai|data science|it|cntt).*(?:khác|giống|học)", re.IGNORECASE),
                re.compile(r"fpt.*(?:có|mở).*ngành", re.IGNORECASE),
            ],
            "weight": 1.15
        }
    },
    {
        "intentId": "scholarship_inquiry",
        "matchers": {
            "keywords": [
                'học bổng', 'scholarship', 'miễn phí', 'tài trợ',
                'giảm học phí', 'hỗ trợ tài chính', 'financial aid'
            ],
            "patterns": [
                re.compile(
                    r"học bổng.*(?:100|full|toàn phần|một phần)", re.IGNORECASE),
                re.compile(r"(?:miễn|giảm).*(?:học phí|100)", re.IGNORECASE),
                re.compile(
                    r"(?:xin|apply).*(?:học bổng|scholarship)", re.IGNORECASE),
            ],
            "weight": 1.3
        }
    },
    {
        "intentId": "career_guidance",
        "matchers": {
            "keywords": [
                'việc làm', 'career', 'job', 'tuyển dụng', 'cơ hội', 'tương lai',
                'dễ xin việc'
            ],
            "patterns": [
                re.compile(r"(?:98|95).*(?:%|phần trăm).*việc làm",
                           re.IGNORECASE),
                re.compile(r"việc làm.*(?:có thật|thực tế|đảm bảo)",
                           re.IGNORECASE),
                re.compile(
                    r"(?:học xong|graduate).*(?:có|have).*(?:chắc chắn|sure|guaranteed).*việc làm", re.IGNORECASE),
            ],
            "weight": 1.0
        }
    },
    {
        "intentId": "deadline_inquiry",
        "matchers": {
            "keywords": [
                'hạn chót', 'deadline', 'hạn nộp', 'hạn cuối', 'thời hạn',
                'khi nào', 'bao giờ', 'tới hạn', 'hết hạn', 'nộp hồ sơ'
            ],
            "patterns": [
                re.compile(
                    r"(?:hạn chót|deadline).*(?:nộp|submit|hồ sơ)", re.IGNORECASE),
                re.compile(
                    r"(?:khi nào|bao giờ).*(?:nộp|submit|hồ sơ)", re.IGNORECASE),
                re.compile(r"(?:hạn|thời hạn).*(?:cuối|chót|nộp)",
                           re.IGNORECASE),
                re.compile(r"(?:nộp hồ sơ).*(?:tới|đến|hạn)", re.IGNORECASE),
            ],
            "weight": 1.2
        }
    },
    {
        "intentId": "comparative_inquiry",
        "matchers": {
            "keywords": [
                'so sánh', 'compare', 'khác nhau', 'giống nhau', 'vs', 'versus',
                'tốt hơn', 'better', 'worse', 'khác biệt'
            ],
            "patterns": [
                re.compile(r"(?:so sánh|compare).*(?:fpt|với|vs)",
                           re.IGNORECASE),
                re.compile(
                    r"fpt.*(?:vs|versus|so với).*(?:bách khoa|bkhn|uit)", re.IGNORECASE),
                re.compile(
                    r"(?:khác nhau|khác biệt).*(?:fpt|trường)", re.IGNORECASE),
            ],
            "weight": 1.1
        }
    },
    {
        "intentId": "general_info",
        "matchers": {
            "keywords": ['fpt', 'university', 'đại học', 'ranking', 'ưu điểm'],
            "patterns": [
                re.compile(
                    r"fpt.*(?:thành lập|từ khi|ranking|tầm nhìn)", re.IGNORECASE),
                re.compile(r"(?:bằng|diploma).*(?:công nhận|valid)",
                           re.IGNORECASE),
            ],
            "weight": 0.8
        }
    }
]
