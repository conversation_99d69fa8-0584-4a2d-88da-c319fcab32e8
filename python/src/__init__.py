"""
FPT University Agent System - Refactored Version

Clean Architecture implementation with dependency injection,
async/await support, and comprehensive type safety.
"""

__version__ = "0.2.0"
__author__ = "nghiahoang"
__email__ = "<EMAIL>"

from .core.domain.entities import IntentResult, SearchCandidate
from .core.domain.services import IntentDetectionService
from .infrastructure.intent_detection import HybridIntentDetectionService
from .infrastructure.config import ConfigLoader

__all__ = [
    "IntentResult",
    "SearchCandidate", 
    "IntentDetectionService",
    "HybridIntentDetectionService",
    "ConfigLoader"
]
