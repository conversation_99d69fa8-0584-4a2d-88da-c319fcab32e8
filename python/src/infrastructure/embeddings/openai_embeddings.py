"""
OpenAI embedding provider implementation
"""

import time
from typing import List, Dict, Any
import structlog

from core.domain.services import EmbeddingProvider
from core.domain.exceptions import EmbeddingError, ServiceUnavailableError
from infrastructure.config.settings import EmbeddingConfig

logger = structlog.get_logger(__name__)

try:
    from openai import AsyncOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logger.warning("openai library not available")


class OpenAIEmbeddingProvider(EmbeddingProvider):
    """
    OpenAI embedding provider with async support and error handling
    """
    
    def __init__(
        self,
        api_key: str,
        model: str = "text-embedding-3-small",
        dimensions: int = 1536,
        batch_size: int = 32,
        max_retries: int = 3,
        timeout: float = 30.0
    ):
        if not OPENAI_AVAILABLE:
            raise ServiceUnavailableError(
                "openai_embeddings",
                "OpenAI library not available. Install with: pip install openai"
            )
        
        self.model = model
        self.dimensions = dimensions
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.timeout = timeout
        
        # Initialize OpenAI client
        self.client = AsyncOpenAI(
            api_key=api_key,
            timeout=timeout,
            max_retries=max_retries
        )
        
        # Statistics
        self._stats = {
            "requests": 0,
            "tokens": 0,
            "errors": 0,
            "total_time": 0.0
        }
        
        logger.info(
            "OpenAI embedding provider initialized",
            model=model,
            dimensions=dimensions,
            batch_size=batch_size,
            max_retries=max_retries
        )
    
    async def embed_text(self, text: str) -> List[float]:
        """
        Generate embedding for single text
        
        Args:
            text: Input text
            
        Returns:
            Embedding vector
        """
        try:
            start_time = time.time()
            
            # Validate input
            if not text or not text.strip():
                raise EmbeddingError(text, "Text cannot be empty")
            
            # Clean text
            cleaned_text = text.strip()
            
            # Make API call
            response = await self.client.embeddings.create(
                model=self.model,
                input=[cleaned_text],
                dimensions=self.dimensions if self.model.startswith("text-embedding-3") else None
            )
            
            # Extract embedding
            embedding = response.data[0].embedding
            
            # Update statistics
            processing_time = time.time() - start_time
            self._stats["requests"] += 1
            self._stats["tokens"] += response.usage.total_tokens
            self._stats["total_time"] += processing_time
            
            logger.debug(
                "OpenAI embedding generated",
                text_length=len(text),
                embedding_dimension=len(embedding),
                tokens_used=response.usage.total_tokens,
                processing_time_ms=processing_time * 1000
            )
            
            return embedding
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error(
                "OpenAI embedding generation failed",
                text_length=len(text) if text else 0,
                error=str(e),
                error_type=type(e).__name__
            )
            raise EmbeddingError(text, str(e))
    
    async def embed_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches
        
        Args:
            texts: List of input texts
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        try:
            start_time = time.time()
            all_embeddings = []
            
            # Process in batches
            for i in range(0, len(texts), self.batch_size):
                batch = texts[i:i + self.batch_size]
                
                # Clean texts
                cleaned_batch = [text.strip() for text in batch if text and text.strip()]
                
                if not cleaned_batch:
                    # Add empty embeddings for empty texts
                    all_embeddings.extend([[0.0] * self.dimensions] * len(batch))
                    continue
                
                # Make API call
                batch_start = time.time()
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=cleaned_batch,
                    dimensions=self.dimensions if self.model.startswith("text-embedding-3") else None
                )
                
                # Extract embeddings
                batch_embeddings = [item.embedding for item in response.data]
                all_embeddings.extend(batch_embeddings)
                
                # Update statistics
                batch_time = time.time() - batch_start
                self._stats["requests"] += 1
                self._stats["tokens"] += response.usage.total_tokens
                self._stats["total_time"] += batch_time
                
                logger.debug(
                    "OpenAI batch embedding completed",
                    batch_size=len(cleaned_batch),
                    tokens_used=response.usage.total_tokens,
                    batch_time_ms=batch_time * 1000
                )
            
            total_time = time.time() - start_time
            
            logger.info(
                "OpenAI batch embedding completed",
                total_texts=len(texts),
                total_embeddings=len(all_embeddings),
                total_time_ms=total_time * 1000,
                avg_time_per_text=total_time / len(texts) * 1000
            )
            
            return all_embeddings
            
        except Exception as e:
            self._stats["errors"] += 1
            logger.error(
                "OpenAI batch embedding failed",
                batch_size=len(texts),
                error=str(e),
                error_type=type(e).__name__
            )
            raise EmbeddingError(f"batch of {len(texts)} texts", str(e))
    
    def get_embedding_dimension(self) -> int:
        """Get embedding vector dimension"""
        return self.dimensions
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get embedding model information"""
        return {
            "provider": "openai",
            "model": self.model,
            "dimensions": self.dimensions,
            "batch_size": self.batch_size,
            "max_retries": self.max_retries,
            "timeout": self.timeout,
            "statistics": self._stats.copy()
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Check OpenAI API health"""
        try:
            # Test with a simple embedding
            test_embedding = await self.embed_text("test")
            
            return {
                "status": "healthy",
                "model": self.model,
                "dimensions": len(test_embedding),
                "test_successful": True
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "model": self.model,
                "error": str(e),
                "test_successful": False
            }
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get detailed usage statistics"""
        total_requests = self._stats["requests"]
        
        return {
            "total_requests": total_requests,
            "total_tokens": self._stats["tokens"],
            "total_errors": self._stats["errors"],
            "total_time_seconds": self._stats["total_time"],
            "avg_time_per_request": (
                self._stats["total_time"] / total_requests 
                if total_requests > 0 else 0
            ),
            "avg_tokens_per_request": (
                self._stats["tokens"] / total_requests 
                if total_requests > 0 else 0
            ),
            "error_rate": (
                self._stats["errors"] / total_requests * 100 
                if total_requests > 0 else 0
            )
        }
    
    def reset_stats(self) -> None:
        """Reset usage statistics"""
        self._stats = {
            "requests": 0,
            "tokens": 0,
            "errors": 0,
            "total_time": 0.0
        }
        logger.info("OpenAI embedding provider stats reset")
    
    async def estimate_cost(self, texts: List[str]) -> Dict[str, Any]:
        """Estimate cost for embedding texts"""
        # Rough token estimation (1 token ≈ 4 characters for English)
        total_chars = sum(len(text) for text in texts)
        estimated_tokens = total_chars // 4
        
        # OpenAI pricing (as of 2024, may change)
        pricing = {
            "text-embedding-3-small": 0.00002,  # per 1K tokens
            "text-embedding-3-large": 0.00013,  # per 1K tokens
            "text-embedding-ada-002": 0.0001,   # per 1K tokens
        }
        
        price_per_1k = pricing.get(self.model, 0.0001)  # Default fallback
        estimated_cost = (estimated_tokens / 1000) * price_per_1k
        
        return {
            "model": self.model,
            "text_count": len(texts),
            "total_characters": total_chars,
            "estimated_tokens": estimated_tokens,
            "price_per_1k_tokens": price_per_1k,
            "estimated_cost_usd": round(estimated_cost, 6)
        }
    
    @classmethod
    def from_config(cls, config: EmbeddingConfig) -> 'OpenAIEmbeddingProvider':
        """Create provider from configuration"""
        return cls(
            api_key=config.openai_api_key,
            model=config.openai_model,
            dimensions=config.openai_dimensions,
            batch_size=config.batch_size,
            max_retries=config.max_retries,
            timeout=config.timeout
        )
