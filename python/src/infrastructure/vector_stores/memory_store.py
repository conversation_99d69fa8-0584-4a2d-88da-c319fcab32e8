"""
In-memory vector store implementation for testing and development
"""

import time
from typing import List, Dict, Any, Optional
import structlog

from core.domain.entities import SearchCandidate
from core.domain.services import VectorStore
from core.domain.exceptions import VectorStoreError
from shared.types import Metadata

logger = structlog.get_logger(__name__)


class MemoryVectorStore(VectorStore):
    """
    In-memory vector store implementation using simple cosine similarity
    """
    
    def __init__(self):
        # Storage: document_id -> {id, vector, text, metadata}
        self.documents: Dict[str, Dict[str, Any]] = {}
        self.next_id = 1
        
        logger.info("Memory vector store initialized")
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        if len(vec1) != len(vec2):
            raise ValueError("Vectors must have the same dimension")
        
        # Dot product
        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        
        # Magnitudes
        magnitude1 = sum(a * a for a in vec1) ** 0.5
        magnitude2 = sum(b * b for b in vec2) ** 0.5
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    async def search(
        self, 
        query_vector: List[float], 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchCandidate]:
        """
        Search for similar vectors using cosine similarity
        
        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filters: Optional metadata filters
            
        Returns:
            List of search candidates sorted by similarity
        """
        try:
            start_time = time.time()
            
            if not self.documents:
                logger.debug("No documents in vector store")
                return []
            
            # Calculate similarities
            similarities = []
            
            for doc_id, doc in self.documents.items():
                # Apply filters if provided
                if filters:
                    doc_metadata = doc.get("metadata", {})
                    if not self._matches_filters(doc_metadata, filters):
                        continue
                
                # Calculate similarity
                similarity = self._cosine_similarity(query_vector, doc["vector"])
                
                similarities.append({
                    "doc_id": doc_id,
                    "similarity": similarity,
                    "document": doc
                })
            
            # Sort by similarity (descending)
            similarities.sort(key=lambda x: x["similarity"], reverse=True)
            
            # Take top_k results
            top_results = similarities[:top_k]
            
            # Convert to SearchCandidate objects
            candidates = []
            for result in top_results:
                doc = result["document"]
                candidate = SearchCandidate(
                    text=doc["text"],
                    intent_id=doc["metadata"].get("intent_id", "unknown"),
                    score=result["similarity"],
                    metadata=doc["metadata"],
                    source="memory_vector_store"
                )
                candidates.append(candidate)
            
            search_time = (time.time() - start_time) * 1000
            
            logger.debug(
                "Memory vector search completed",
                query_dimension=len(query_vector),
                total_documents=len(self.documents),
                filtered_documents=len(similarities),
                top_k=top_k,
                results_count=len(candidates),
                search_time_ms=search_time
            )
            
            return candidates
            
        except Exception as e:
            logger.error("Memory vector search failed", error=str(e))
            raise VectorStoreError("search", str(e))
    
    def _matches_filters(self, metadata: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """Check if document metadata matches filters"""
        for key, value in filters.items():
            if key not in metadata:
                return False
            
            if isinstance(value, list):
                # OR condition for list values
                if metadata[key] not in value:
                    return False
            else:
                # Exact match
                if metadata[key] != value:
                    return False
        
        return True
    
    async def add_documents(
        self, 
        texts: List[str], 
        vectors: List[List[float]],
        metadata: List[Metadata]
    ) -> None:
        """
        Add documents to vector store
        
        Args:
            texts: Document texts
            vectors: Embedding vectors
            metadata: Document metadata
        """
        try:
            if not (len(texts) == len(vectors) == len(metadata)):
                raise ValueError("texts, vectors, and metadata must have the same length")
            
            added_count = 0
            
            for text, vector, meta in zip(texts, vectors, metadata):
                doc_id = str(self.next_id)
                self.next_id += 1
                
                self.documents[doc_id] = {
                    "id": doc_id,
                    "text": text,
                    "vector": vector,
                    "metadata": meta,
                    "created_at": time.time()
                }
                
                added_count += 1
            
            logger.info(
                "Documents added to memory vector store",
                added_count=added_count,
                total_documents=len(self.documents)
            )
            
        except Exception as e:
            logger.error("Failed to add documents", error=str(e))
            raise VectorStoreError("add_documents", str(e))
    
    async def delete_documents(self, document_ids: List[str]) -> None:
        """Delete documents by IDs"""
        try:
            deleted_count = 0
            
            for doc_id in document_ids:
                if doc_id in self.documents:
                    del self.documents[doc_id]
                    deleted_count += 1
            
            logger.info(
                "Documents deleted from memory vector store",
                deleted_count=deleted_count,
                remaining_documents=len(self.documents)
            )
            
        except Exception as e:
            logger.error("Failed to delete documents", error=str(e))
            raise VectorStoreError("delete_documents", str(e))
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get collection information and statistics"""
        try:
            if not self.documents:
                return {
                    "type": "memory",
                    "document_count": 0,
                    "vector_dimension": None,
                    "storage_size_bytes": 0
                }
            
            # Get vector dimension from first document
            first_doc = next(iter(self.documents.values()))
            vector_dimension = len(first_doc["vector"])
            
            # Estimate storage size
            import sys
            storage_size = sum(sys.getsizeof(doc) for doc in self.documents.values())
            
            # Analyze metadata
            intent_distribution = {}
            for doc in self.documents.values():
                intent_id = doc["metadata"].get("intent_id", "unknown")
                intent_distribution[intent_id] = intent_distribution.get(intent_id, 0) + 1
            
            return {
                "type": "memory",
                "document_count": len(self.documents),
                "vector_dimension": vector_dimension,
                "storage_size_bytes": storage_size,
                "intent_distribution": intent_distribution,
                "next_id": self.next_id
            }
            
        except Exception as e:
            logger.error("Failed to get collection info", error=str(e))
            raise VectorStoreError("get_collection_info", str(e))
    
    def clear(self) -> None:
        """Clear all documents"""
        document_count = len(self.documents)
        self.documents.clear()
        self.next_id = 1
        
        logger.info("Memory vector store cleared", cleared_documents=document_count)
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get document by ID"""
        return self.documents.get(doc_id)
    
    def get_all_documents(self) -> List[Dict[str, Any]]:
        """Get all documents"""
        return list(self.documents.values())
    
    async def update_document_metadata(
        self, 
        doc_id: str, 
        new_metadata: Dict[str, Any]
    ) -> None:
        """Update document metadata"""
        try:
            if doc_id not in self.documents:
                raise ValueError(f"Document {doc_id} not found")
            
            self.documents[doc_id]["metadata"].update(new_metadata)
            
            logger.debug("Document metadata updated", doc_id=doc_id)
            
        except Exception as e:
            logger.error("Failed to update document metadata", doc_id=doc_id, error=str(e))
            raise VectorStoreError("update_metadata", str(e))
    
    async def search_by_metadata(
        self, 
        filters: Dict[str, Any],
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Search documents by metadata only"""
        try:
            matching_docs = []
            
            for doc in self.documents.values():
                if self._matches_filters(doc["metadata"], filters):
                    matching_docs.append(doc)
                    
                    if len(matching_docs) >= limit:
                        break
            
            logger.debug(
                "Metadata search completed",
                filters=filters,
                matches=len(matching_docs),
                limit=limit
            )
            
            return matching_docs
            
        except Exception as e:
            logger.error("Metadata search failed", error=str(e))
            raise VectorStoreError("search_by_metadata", str(e))
