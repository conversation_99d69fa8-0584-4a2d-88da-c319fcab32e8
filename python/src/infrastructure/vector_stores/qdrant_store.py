"""
Qdrant vector store implementation
"""

import time
from typing import List, Dict, Any, Optional
import structlog

from core.domain.entities import SearchCandidate
from core.domain.services import VectorStore
from core.domain.exceptions import VectorStoreError, ServiceUnavailableError
from shared.types import Metadata

logger = structlog.get_logger(__name__)

try:
    from qdrant_client import AsyncQdrantClient
    from qdrant_client.models import (
        Distance, VectorParams, CreateCollection, PointStruct,
        Filter, FieldCondition, SearchRequest, UpdateResult
    )
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    logger.warning("qdrant-client not available")


class QdrantVectorStore(VectorStore):
    """
    Qdrant vector store implementation with async support
    """
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 6333,
        collection_name: str = "fpt_intents",
        vector_dimension: int = 1536,
        distance_metric: str = "Cosine",
        timeout: float = 10.0,
        api_key: Optional[str] = None
    ):
        if not QDRANT_AVAILABLE:
            raise ServiceUnavailableError(
                "qdrant",
                "Qdrant client not available. Install with: pip install qdrant-client"
            )
        
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.vector_dimension = vector_dimension
        self.distance_metric = distance_metric
        self.timeout = timeout
        
        # Initialize client
        self.client = AsyncQdrantClient(
            host=host,
            port=port,
            api_key=api_key,
            timeout=timeout
        )
        
        # Distance mapping
        distance_map = {
            "Cosine": Distance.COSINE,
            "Euclidean": Distance.EUCLID,
            "Dot": Distance.DOT
        }
        self.distance = distance_map.get(distance_metric, Distance.COSINE)
        
        logger.info(
            "Qdrant vector store initialized",
            host=host,
            port=port,
            collection=collection_name,
            dimension=vector_dimension,
            distance=distance_metric
        )
    
    async def ensure_collection_exists(self) -> None:
        """Ensure collection exists, create if not"""
        try:
            # Check if collection exists
            collections = await self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                logger.info("Creating Qdrant collection", name=self.collection_name)
                
                await self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_dimension,
                        distance=self.distance
                    )
                )
                
                logger.info("Qdrant collection created", name=self.collection_name)
            else:
                logger.debug("Qdrant collection exists", name=self.collection_name)
                
        except Exception as e:
            logger.error("Failed to ensure collection exists", error=str(e))
            raise VectorStoreError("ensure_collection", str(e))
    
    async def search(
        self, 
        query_vector: List[float], 
        top_k: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchCandidate]:
        """
        Search for similar vectors in Qdrant
        
        Args:
            query_vector: Query embedding vector
            top_k: Number of results to return
            filters: Optional metadata filters
            
        Returns:
            List of search candidates sorted by similarity
        """
        try:
            start_time = time.time()
            
            # Ensure collection exists
            await self.ensure_collection_exists()
            
            # Build filter if provided
            qdrant_filter = None
            if filters:
                qdrant_filter = self._build_filter(filters)
            
            # Perform search
            search_result = await self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=top_k,
                query_filter=qdrant_filter,
                with_payload=True,
                with_vectors=False
            )
            
            # Convert to SearchCandidate objects
            candidates = []
            for point in search_result:
                payload = point.payload or {}
                
                candidate = SearchCandidate(
                    text=payload.get("text", ""),
                    intent_id=payload.get("intent_id", "unknown"),
                    score=point.score,
                    metadata=payload.get("metadata", {}),
                    source="qdrant"
                )
                candidates.append(candidate)
            
            search_time = (time.time() - start_time) * 1000
            
            logger.debug(
                "Qdrant search completed",
                query_dimension=len(query_vector),
                top_k=top_k,
                results_count=len(candidates),
                search_time_ms=search_time,
                filters=filters
            )
            
            return candidates
            
        except Exception as e:
            logger.error("Qdrant search failed", error=str(e))
            raise VectorStoreError("search", str(e))
    
    def _build_filter(self, filters: Dict[str, Any]) -> Filter:
        """Build Qdrant filter from dictionary"""
        conditions = []
        
        for key, value in filters.items():
            if isinstance(value, list):
                # OR condition for list values
                for v in value:
                    conditions.append(
                        FieldCondition(key=key, match={"value": v})
                    )
            else:
                # Exact match
                conditions.append(
                    FieldCondition(key=key, match={"value": value})
                )
        
        return Filter(must=conditions) if conditions else None
    
    async def add_documents(
        self, 
        texts: List[str], 
        vectors: List[List[float]],
        metadata: List[Metadata]
    ) -> None:
        """
        Add documents to Qdrant collection
        
        Args:
            texts: Document texts
            vectors: Embedding vectors
            metadata: Document metadata
        """
        try:
            if not (len(texts) == len(vectors) == len(metadata)):
                raise ValueError("texts, vectors, and metadata must have the same length")
            
            # Ensure collection exists
            await self.ensure_collection_exists()
            
            # Create points
            points = []
            for i, (text, vector, meta) in enumerate(zip(texts, vectors, metadata)):
                point = PointStruct(
                    id=int(time.time() * 1000000) + i,  # Unique ID based on timestamp
                    vector=vector,
                    payload={
                        "text": text,
                        "intent_id": meta.get("intent_id", "unknown"),
                        "metadata": meta,
                        "created_at": time.time()
                    }
                )
                points.append(point)
            
            # Upsert points
            result = await self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(
                "Documents added to Qdrant",
                added_count=len(points),
                operation_id=result.operation_id if hasattr(result, 'operation_id') else None
            )
            
        except Exception as e:
            logger.error("Failed to add documents to Qdrant", error=str(e))
            raise VectorStoreError("add_documents", str(e))
    
    async def delete_documents(self, document_ids: List[str]) -> None:
        """Delete documents by IDs"""
        try:
            # Convert string IDs to integers if needed
            point_ids = []
            for doc_id in document_ids:
                try:
                    point_ids.append(int(doc_id))
                except ValueError:
                    logger.warning("Invalid document ID format", doc_id=doc_id)
            
            if not point_ids:
                logger.warning("No valid document IDs to delete")
                return
            
            # Delete points
            result = await self.client.delete(
                collection_name=self.collection_name,
                points_selector=point_ids
            )
            
            logger.info(
                "Documents deleted from Qdrant",
                deleted_count=len(point_ids),
                operation_id=result.operation_id if hasattr(result, 'operation_id') else None
            )
            
        except Exception as e:
            logger.error("Failed to delete documents from Qdrant", error=str(e))
            raise VectorStoreError("delete_documents", str(e))
    
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get collection information and statistics"""
        try:
            # Get collection info
            collection_info = await self.client.get_collection(self.collection_name)
            
            # Get collection statistics
            try:
                stats = await self.client.get_collection_stats(self.collection_name)
                point_count = stats.points_count
                vector_count = stats.vectors_count
            except:
                point_count = 0
                vector_count = 0
            
            return {
                "type": "qdrant",
                "collection_name": self.collection_name,
                "status": collection_info.status,
                "point_count": point_count,
                "vector_count": vector_count,
                "vector_dimension": self.vector_dimension,
                "distance_metric": self.distance_metric,
                "config": {
                    "host": self.host,
                    "port": self.port,
                    "timeout": self.timeout
                }
            }
            
        except Exception as e:
            logger.error("Failed to get Qdrant collection info", error=str(e))
            raise VectorStoreError("get_collection_info", str(e))
    
    async def health_check(self) -> Dict[str, Any]:
        """Check Qdrant health"""
        try:
            # Try to get collections
            collections = await self.client.get_collections()
            
            return {
                "status": "healthy",
                "collections_count": len(collections.collections),
                "target_collection_exists": self.collection_name in [c.name for c in collections.collections]
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
    
    async def scroll_documents(
        self, 
        limit: int = 100,
        offset: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Scroll through documents in collection"""
        try:
            qdrant_filter = None
            if filters:
                qdrant_filter = self._build_filter(filters)
            
            result = await self.client.scroll(
                collection_name=self.collection_name,
                limit=limit,
                offset=offset,
                scroll_filter=qdrant_filter,
                with_payload=True,
                with_vectors=False
            )
            
            documents = []
            for point in result[0]:  # result is (points, next_page_offset)
                payload = point.payload or {}
                documents.append({
                    "id": str(point.id),
                    "text": payload.get("text", ""),
                    "intent_id": payload.get("intent_id", "unknown"),
                    "metadata": payload.get("metadata", {}),
                    "score": getattr(point, 'score', None)
                })
            
            return {
                "documents": documents,
                "next_offset": result[1],  # next page offset
                "count": len(documents)
            }
            
        except Exception as e:
            logger.error("Failed to scroll Qdrant documents", error=str(e))
            raise VectorStoreError("scroll_documents", str(e))
    
    async def close(self) -> None:
        """Close Qdrant client connection"""
        try:
            await self.client.close()
            logger.info("Qdrant client connection closed")
        except Exception as e:
            logger.error("Failed to close Qdrant connection", error=str(e))
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.ensure_collection_exists()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
