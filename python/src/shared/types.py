"""
Common type definitions and type aliases
"""

from typing import Dict, List, Optional, Union, Any, TypeVar, Generic
from enum import Enum
from dataclasses import dataclass
from datetime import datetime

# Type variables
T = TypeVar('T')
K = TypeVar('K')
V = TypeVar('V')

# Basic type aliases
QueryText = str
IntentId = str
Confidence = float
Score = float
Timestamp = float
CacheKey = str

# Collection types
Metadata = Dict[str, Any]
QueryParams = Dict[str, Union[str, int, float, bool]]
Headers = Dict[str, str]
Tags = List[str]

# Enums
class DetectionMethod(str, Enum):
    """Intent detection methods"""
    RULE = "rule"
    VECTOR = "vector" 
    RERANK = "rerank"
    HYBRID = "hybrid"
    FALLBACK = "fallback"
    CACHE = "cache"

class ConfidenceLevel(str, Enum):
    """Confidence level categories"""
    VERY_HIGH = "very_high"  # >= 0.9
    HIGH = "high"            # >= 0.7
    MEDIUM = "medium"        # >= 0.5
    LOW = "low"              # >= 0.3
    VERY_LOW = "very_low"    # < 0.3

class IntentCategory(str, Enum):
    """Intent categories for FPT University"""
    TUITION_INQUIRY = "tuition_inquiry"
    CAMPUS_INFO = "campus_info"
    PROGRAM_SEARCH = "program_search"
    PROGRAM_REQUIREMENTS = "program_requirements"
    SCHOLARSHIP_INQUIRY = "scholarship_inquiry"
    DEADLINE_INQUIRY = "deadline_inquiry"
    CAREER_GUIDANCE = "career_guidance"
    COMPARATIVE_INQUIRY = "comparative_inquiry"
    GENERAL_INFO = "general_info"

class LogLevel(str, Enum):
    """Logging levels"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

# Data classes for structured data
@dataclass(frozen=True)
class TimeRange:
    """Time range for analytics"""
    start: datetime
    end: datetime
    
    def duration_seconds(self) -> float:
        return (self.end - self.start).total_seconds()

@dataclass(frozen=True)
class PerformanceMetrics:
    """Performance metrics for monitoring"""
    total_requests: int
    cache_hits: int
    cache_misses: int
    avg_response_time_ms: float
    p95_response_time_ms: float
    error_rate: float
    
    @property
    def cache_hit_rate(self) -> float:
        total = self.cache_hits + self.cache_misses
        return self.cache_hits / total if total > 0 else 0.0

# Generic result types
@dataclass(frozen=True)
class Result(Generic[T]):
    """Generic result type for error handling"""
    success: bool
    data: Optional[T] = None
    error: Optional[str] = None
    error_code: Optional[str] = None
    
    @classmethod
    def ok(cls, data: T) -> 'Result[T]':
        return cls(success=True, data=data)
    
    @classmethod
    def error(cls, error: str, error_code: Optional[str] = None) -> 'Result[T]':
        return cls(success=False, error=error, error_code=error_code)
    
    def is_ok(self) -> bool:
        return self.success
    
    def is_error(self) -> bool:
        return not self.success

# Pagination types
@dataclass(frozen=True)
class PaginationParams:
    """Pagination parameters"""
    page: int = 1
    limit: int = 10
    offset: Optional[int] = None
    
    def __post_init__(self):
        if self.offset is None:
            object.__setattr__(self, 'offset', (self.page - 1) * self.limit)

@dataclass(frozen=True)
class PaginatedResult(Generic[T]):
    """Paginated result wrapper"""
    items: List[T]
    total: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool
    
    @property
    def total_pages(self) -> int:
        return (self.total + self.limit - 1) // self.limit
