"""
Async utilities for performance optimization
"""

import asyncio
import time
from typing import List, TypeVar, Callable, Any, Optional, Dict
from contextlib import asynccontextmanager
import structlog

logger = structlog.get_logger(__name__)

T = TypeVar('T')


class AsyncBatchProcessor:
    """
    Batch processor for efficient async operations
    """
    
    def __init__(
        self,
        batch_size: int = 10,
        max_concurrency: int = 5,
        timeout_seconds: float = 30.0
    ):
        self.batch_size = batch_size
        self.max_concurrency = max_concurrency
        self.timeout_seconds = timeout_seconds
        self.semaphore = asyncio.Semaphore(max_concurrency)
    
    async def process_batch(
        self,
        items: List[T],
        processor: Callable[[List[T]], Any],
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> List[Any]:
        """
        Process items in batches with concurrency control
        
        Args:
            items: Items to process
            processor: Async function to process each batch
            progress_callback: Optional progress callback
            
        Returns:
            List of results
        """
        if not items:
            return []
        
        # Split into batches
        batches = [
            items[i:i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]
        
        logger.info(
            "Starting batch processing",
            total_items=len(items),
            batch_count=len(batches),
            batch_size=self.batch_size,
            max_concurrency=self.max_concurrency
        )
        
        # Process batches concurrently
        async def process_single_batch(batch_idx: int, batch: List[T]) -> Any:
            async with self.semaphore:
                try:
                    start_time = time.time()
                    result = await asyncio.wait_for(
                        processor(batch),
                        timeout=self.timeout_seconds
                    )
                    processing_time = time.time() - start_time
                    
                    if progress_callback:
                        progress_callback(batch_idx + 1, len(batches))
                    
                    logger.debug(
                        "Batch processed",
                        batch_idx=batch_idx,
                        batch_size=len(batch),
                        processing_time_ms=processing_time * 1000
                    )
                    
                    return result
                    
                except asyncio.TimeoutError:
                    logger.error(
                        "Batch processing timeout",
                        batch_idx=batch_idx,
                        timeout_seconds=self.timeout_seconds
                    )
                    raise
                except Exception as e:
                    logger.error(
                        "Batch processing failed",
                        batch_idx=batch_idx,
                        error=str(e)
                    )
                    raise
        
        # Execute all batches
        start_time = time.time()
        tasks = [
            process_single_batch(i, batch)
            for i, batch in enumerate(batches)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time
        
        # Handle exceptions
        successful_results = []
        failed_count = 0
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Batch {i} failed", error=str(result))
                failed_count += 1
            else:
                successful_results.append(result)
        
        logger.info(
            "Batch processing completed",
            total_batches=len(batches),
            successful_batches=len(successful_results),
            failed_batches=failed_count,
            total_time_ms=total_time * 1000,
            avg_time_per_batch=total_time / len(batches) * 1000
        )
        
        return successful_results


class AsyncRetryManager:
    """
    Retry manager for async operations with exponential backoff
    """
    
    def __init__(
        self,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True
    ):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
    
    async def retry(
        self,
        operation: Callable[[], Any],
        operation_name: str = "operation",
        retriable_exceptions: tuple = (Exception,)
    ) -> Any:
        """
        Retry an async operation with exponential backoff
        
        Args:
            operation: Async function to retry
            operation_name: Name for logging
            retriable_exceptions: Exceptions that should trigger retry
            
        Returns:
            Operation result
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                start_time = time.time()
                result = await operation()
                execution_time = time.time() - start_time
                
                if attempt > 0:
                    logger.info(
                        "Operation succeeded after retry",
                        operation=operation_name,
                        attempt=attempt + 1,
                        execution_time_ms=execution_time * 1000
                    )
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if exception is retriable
                if not isinstance(e, retriable_exceptions):
                    logger.error(
                        "Non-retriable exception",
                        operation=operation_name,
                        attempt=attempt + 1,
                        error=str(e),
                        error_type=type(e).__name__
                    )
                    raise
                
                # Don't retry on last attempt
                if attempt == self.max_retries:
                    break
                
                # Calculate delay
                delay = min(
                    self.base_delay * (self.exponential_base ** attempt),
                    self.max_delay
                )
                
                # Add jitter
                if self.jitter:
                    import random
                    delay *= (0.5 + random.random() * 0.5)
                
                logger.warning(
                    "Operation failed, retrying",
                    operation=operation_name,
                    attempt=attempt + 1,
                    max_retries=self.max_retries,
                    delay_seconds=delay,
                    error=str(e)
                )
                
                await asyncio.sleep(delay)
        
        # All retries exhausted
        logger.error(
            "Operation failed after all retries",
            operation=operation_name,
            max_retries=self.max_retries,
            final_error=str(last_exception)
        )
        
        raise last_exception


class AsyncCircuitBreaker:
    """
    Circuit breaker pattern for async operations
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: type = Exception
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time: Optional[float] = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    async def call(self, operation: Callable[[], Any], operation_name: str = "operation") -> Any:
        """
        Execute operation through circuit breaker
        
        Args:
            operation: Async function to execute
            operation_name: Name for logging
            
        Returns:
            Operation result
        """
        # Check circuit state
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
                logger.info(
                    "Circuit breaker transitioning to HALF_OPEN",
                    operation=operation_name
                )
            else:
                logger.warning(
                    "Circuit breaker is OPEN, rejecting call",
                    operation=operation_name,
                    failure_count=self.failure_count
                )
                raise Exception(f"Circuit breaker is OPEN for {operation_name}")
        
        try:
            result = await operation()
            
            # Success - reset failure count
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                logger.info(
                    "Circuit breaker reset to CLOSED",
                    operation=operation_name
                )
            
            self.failure_count = 0
            return result
            
        except self.expected_exception as e:
            self._record_failure(operation_name)
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True
        
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _record_failure(self, operation_name: str) -> None:
        """Record a failure and potentially open circuit"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.error(
                "Circuit breaker opened due to failures",
                operation=operation_name,
                failure_count=self.failure_count,
                threshold=self.failure_threshold
            )
    
    def get_state(self) -> Dict[str, Any]:
        """Get circuit breaker state"""
        return {
            "state": self.state,
            "failure_count": self.failure_count,
            "failure_threshold": self.failure_threshold,
            "last_failure_time": self.last_failure_time,
            "recovery_timeout": self.recovery_timeout
        }


@asynccontextmanager
async def async_timeout(seconds: float):
    """
    Async context manager for timeout operations
    """
    try:
        async with asyncio.timeout(seconds):
            yield
    except asyncio.TimeoutError:
        logger.error(f"Operation timed out after {seconds} seconds")
        raise


async def gather_with_concurrency(
    tasks: List[Any],
    max_concurrency: int = 10,
    return_exceptions: bool = True
) -> List[Any]:
    """
    Execute tasks with limited concurrency
    
    Args:
        tasks: List of coroutines or tasks
        max_concurrency: Maximum concurrent tasks
        return_exceptions: Whether to return exceptions as results
        
    Returns:
        List of results
    """
    semaphore = asyncio.Semaphore(max_concurrency)
    
    async def limited_task(task):
        async with semaphore:
            if asyncio.iscoroutine(task):
                return await task
            else:
                return await task
    
    limited_tasks = [limited_task(task) for task in tasks]
    return await asyncio.gather(*limited_tasks, return_exceptions=return_exceptions)


class AsyncRateLimiter:
    """
    Rate limiter for async operations
    """
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self._lock = asyncio.Lock()
    
    async def acquire(self) -> None:
        """Acquire permission to make a call"""
        async with self._lock:
            now = time.time()
            
            # Remove old calls outside time window
            self.calls = [call_time for call_time in self.calls if now - call_time < self.time_window]
            
            # Check if we can make a call
            if len(self.calls) >= self.max_calls:
                # Calculate wait time
                oldest_call = min(self.calls)
                wait_time = self.time_window - (now - oldest_call)
                
                logger.debug(
                    "Rate limit reached, waiting",
                    current_calls=len(self.calls),
                    max_calls=self.max_calls,
                    wait_time_seconds=wait_time
                )
                
                await asyncio.sleep(wait_time)
                return await self.acquire()  # Recursive call after waiting
            
            # Record this call
            self.calls.append(now)
    
    @asynccontextmanager
    async def limit(self):
        """Context manager for rate limiting"""
        await self.acquire()
        try:
            yield
        finally:
            pass  # Nothing to cleanup


def create_task_with_name(coro, name: str) -> asyncio.Task:
    """Create a named task for better debugging"""
    task = asyncio.create_task(coro)
    task.set_name(name)
    return task


async def wait_for_any(tasks: List[asyncio.Task], timeout: Optional[float] = None) -> Any:
    """
    Wait for any task to complete (similar to asyncio.wait with FIRST_COMPLETED)
    
    Args:
        tasks: List of tasks
        timeout: Optional timeout
        
    Returns:
        Result of first completed task
    """
    if not tasks:
        raise ValueError("No tasks provided")
    
    try:
        done, pending = await asyncio.wait(
            tasks,
            return_when=asyncio.FIRST_COMPLETED,
            timeout=timeout
        )
        
        # Cancel pending tasks
        for task in pending:
            task.cancel()
        
        if not done:
            raise asyncio.TimeoutError("No task completed within timeout")
        
        # Return result of first completed task
        completed_task = done.pop()
        return await completed_task
        
    except Exception as e:
        # Cancel all tasks on error
        for task in tasks:
            if not task.done():
                task.cancel()
        raise
