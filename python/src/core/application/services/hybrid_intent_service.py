"""
Hybrid Intent Detection Service
Combines rule-based and vector-based intent detection
"""

import time
import hashlib
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from core.domain.entities import IntentResult, DetectionContext, RuleMatch, SearchCandidate
from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
from infrastructure.vector_stores.qdrant_store import QdrantVectorStore
from infrastructure.embeddings.openai_embeddings import OpenAIEmbeddingService
from infrastructure.caching.memory_cache import MemoryCacheService
from shared.types import DetectionMethod, QueryText, IntentId
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl


@dataclass
class HybridConfig:
    """Configuration for hybrid intent detection"""
    rule_high_confidence_threshold: float = 0.7
    rule_medium_confidence_threshold: float = 0.3
    vector_confidence_threshold: float = 0.6
    early_exit_threshold: float = 0.9
    vector_top_k: int = 5
    enable_caching: bool = True
    cache_ttl_seconds: int = 300


class HybridIntentDetectionService:
    """
    Hybrid intent detection service combining rule-based and vector search
    """
    
    def __init__(
        self,
        rule_detector: RuleBasedDetectorImpl,
        vector_store: Optional[QdrantVectorStore] = None,
        embedding_service: Optional[OpenAIEmbeddingService] = None,
        cache_service: Optional[MemoryCacheService] = None,
        text_processor: Optional[VietnameseTextProcessor] = None,
        metrics_collector: Optional[MetricsCollectorImpl] = None,
        config: Optional[HybridConfig] = None
    ):
        self.rule_detector = rule_detector
        self.vector_store = vector_store
        self.embedding_service = embedding_service
        self.cache_service = cache_service
        self.text_processor = text_processor or VietnameseTextProcessor()
        self.metrics_collector = metrics_collector
        self.config = config or HybridConfig()
        
        # Check vector search availability
        self.vector_search_enabled = (
            self.vector_store and 
            self.vector_store.available and 
            self.embedding_service and 
            self.embedding_service.available
        )
        
        print(f"🔧 Hybrid service initialized:")
        print(f"   - Rule-based: ✅")
        print(f"   - Vector search: {'✅' if self.vector_search_enabled else '❌'}")
        print(f"   - Caching: {'✅' if self.cache_service else '❌'}")
    
    async def detect_intent(self, context: DetectionContext) -> IntentResult:
        """
        Main intent detection method using hybrid approach
        
        Args:
            context: Detection context with query and metadata
            
        Returns:
            IntentResult with detected intent and confidence
        """
        start_time = time.time()
        query = context.query
        
        try:
            # Step 1: Check cache first
            if self.config.enable_caching and self.cache_service:
                cached_result = await self._get_cached_result(query)
                if cached_result:
                    if self.metrics_collector:
                        self.metrics_collector.increment_counter("cache_hits")
                    return cached_result
            
            # Step 2: Check for irrelevant queries
            if self.text_processor.is_irrelevant_query(query):
                result = self._create_fallback_result(0.1, DetectionMethod.FALLBACK)
                await self._cache_result(query, result)
                return result
            
            # Step 3: Rule-based detection
            rule_match = await self.rule_detector.detect(query)
            
            if self.metrics_collector:
                self.metrics_collector.increment_counter("rule_detections")
            
            # Early exit for very high confidence rule matches
            if rule_match and rule_match.score >= self.config.early_exit_threshold:
                result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight
                    }
                )
                await self._cache_result(query, result)
                return result
            
            # Step 4: High confidence rule match
            if rule_match and rule_match.score >= self.config.rule_high_confidence_threshold:
                result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight
                    }
                )
                await self._cache_result(query, result)
                return result
            
            # Step 5: Vector search fallback
            vector_result = await self._vector_search(query)
            
            if vector_result:
                # Prefer vector result if it has higher confidence
                if not rule_match or vector_result.confidence > rule_match.score:
                    await self._cache_result(query, vector_result)
                    return vector_result
            
            # Step 6: Medium confidence rule match
            if rule_match and rule_match.score >= self.config.rule_medium_confidence_threshold:
                result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight,
                        "fallback_reason": "medium_confidence_rule"
                    }
                )
                await self._cache_result(query, result)
                return result
            
            # Step 7: Final fallback
            result = self._create_fallback_result(0.2, DetectionMethod.FALLBACK)
            await self._cache_result(query, result)
            return result
            
        except Exception as e:
            print(f"❌ Intent detection failed: {e}")
            if self.metrics_collector:
                self.metrics_collector.increment_counter("detection_errors")
            
            return self._create_fallback_result(0.1, DetectionMethod.FALLBACK)
        
        finally:
            # Record performance metrics
            if self.metrics_collector:
                duration_ms = (time.time() - start_time) * 1000
                self.metrics_collector.record_histogram("detection_duration_ms", duration_ms)
    
    async def _vector_search(self, query: str) -> Optional[IntentResult]:
        """Perform vector search for intent detection"""
        if not self.vector_search_enabled:
            return None
        
        try:
            # Generate query embedding
            query_embedding = await self.embedding_service.embed_text(query)
            if not query_embedding:
                return None
            
            # Search in vector store
            candidates = await self.vector_store.search(
                query_vector=query_embedding,
                top_k=self.config.vector_top_k,
                score_threshold=self.config.vector_confidence_threshold
            )
            
            if self.metrics_collector:
                self.metrics_collector.increment_counter("vector_searches")
                self.metrics_collector.record_histogram(
                    "vector_candidates_found", 
                    len(candidates)
                )
            
            if not candidates:
                return None
            
            # Use best candidate
            best_candidate = candidates[0]
            
            if best_candidate.score >= self.config.vector_confidence_threshold:
                return IntentResult(
                    id=best_candidate.intent_id,
                    confidence=best_candidate.normalized_score,
                    method=DetectionMethod.VECTOR,
                    metadata={
                        "vector_score": best_candidate.score,
                        "source_text": best_candidate.text,
                        "candidates_count": len(candidates)
                    }
                )
            
            return None
            
        except Exception as e:
            print(f"❌ Vector search failed: {e}")
            if self.metrics_collector:
                self.metrics_collector.increment_counter("vector_search_errors")
            return None
    
    async def _get_cached_result(self, query: str) -> Optional[IntentResult]:
        """Get cached result for query"""
        if not self.cache_service:
            return None
        
        cache_key = self._generate_cache_key(query)
        cached_data = await self.cache_service.get(cache_key)
        
        if cached_data:
            return IntentResult(**cached_data)
        
        return None
    
    async def _cache_result(self, query: str, result: IntentResult) -> None:
        """Cache result for query"""
        if not self.cache_service or not self.config.enable_caching:
            return
        
        cache_key = self._generate_cache_key(query)
        cache_data = {
            "id": result.id,
            "confidence": result.confidence,
            "method": result.method,
            "metadata": result.metadata,
            "timestamp": result.timestamp
        }
        
        await self.cache_service.set(
            cache_key, 
            cache_data, 
            ttl_seconds=self.config.cache_ttl_seconds
        )
    
    def _generate_cache_key(self, query: str) -> str:
        """Generate cache key for query"""
        normalized_query = self.text_processor.normalize_vietnamese(query)
        return f"intent:{hashlib.md5(normalized_query.encode()).hexdigest()}"
    
    def _create_fallback_result(self, confidence: float, method: DetectionMethod) -> IntentResult:
        """Create fallback intent result"""
        return IntentResult(
            id="general_info",
            confidence=confidence,
            method=method,
            metadata={"fallback": True}
        )
    
    async def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        stats = {}
        
        if self.metrics_collector:
            stats.update(self.metrics_collector.get_all_metrics())
        
        if self.cache_service:
            cache_stats = await self.cache_service.get_stats()
            stats["cache"] = cache_stats
        
        if self.vector_store:
            vector_info = await self.vector_store.get_collection_info()
            stats["vector_store"] = vector_info
        
        return stats
