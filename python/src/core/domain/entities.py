"""
Core domain entities for intent detection system
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from shared.types import (
    QueryText, IntentId, Confidence, Score, Metadata,
    DetectionMethod, ConfidenceLevel, IntentCategory
)


@dataclass(frozen=True)
class IntentResult:
    """
    Core entity representing the result of intent detection
    """
    id: IntentId
    confidence: Confidence
    method: DetectionMethod
    category: Optional[IntentCategory] = None
    metadata: Metadata = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    processing_time_ms: Optional[float] = None
    
    def __post_init__(self):
        # Validate confidence range
        if not 0.0 <= self.confidence <= 1.0:
            raise ValueError(f"Confidence must be between 0.0 and 1.0, got {self.confidence}")
    
    @property
    def confidence_level(self) -> ConfidenceLevel:
        """Get confidence level category"""
        if self.confidence >= 0.9:
            return ConfidenceLevel.VERY_HIGH
        elif self.confidence >= 0.7:
            return ConfidenceLevel.HIGH
        elif self.confidence >= 0.5:
            return ConfidenceLevel.MEDIUM
        elif self.confidence >= 0.3:
            return ConfidenceLevel.LOW
        else:
            return ConfidenceLevel.VERY_LOW
    
    def is_high_confidence(self, threshold: float = 0.7) -> bool:
        """Check if result has high confidence"""
        return self.confidence >= threshold
    
    def with_metadata(self, **kwargs) -> 'IntentResult':
        """Create new instance with additional metadata"""
        new_metadata = {**self.metadata, **kwargs}
        return IntentResult(
            id=self.id,
            confidence=self.confidence,
            method=self.method,
            category=self.category,
            metadata=new_metadata,
            timestamp=self.timestamp,
            processing_time_ms=self.processing_time_ms
        )


@dataclass(frozen=True)
class SearchCandidate:
    """
    Entity representing a candidate from vector search
    """
    text: str
    intent_id: IntentId
    score: Score
    metadata: Metadata = field(default_factory=dict)
    source: str = "unknown"
    
    def __post_init__(self):
        # Validate score (can be negative for some similarity metrics)
        if not isinstance(self.score, (int, float)):
            raise ValueError(f"Score must be numeric, got {type(self.score)}")
    
    @property
    def normalized_score(self) -> float:
        """Get normalized score between 0 and 1"""
        # For cosine similarity, scores are typically between -1 and 1
        # Normalize to 0-1 range
        return max(0.0, min(1.0, (self.score + 1) / 2))


@dataclass(frozen=True)
class RuleMatch:
    """
    Entity representing a rule-based match
    """
    intent_id: IntentId
    matched_keywords: List[str]
    matched_patterns: List[str]
    score: Score
    weight: float
    position: int = 0  # Position of first match in query
    
    @property
    def match_strength(self) -> str:
        """Get qualitative match strength"""
        if self.score >= 0.8:
            return "strong"
        elif self.score >= 0.6:
            return "moderate"
        elif self.score >= 0.4:
            return "weak"
        else:
            return "very_weak"


@dataclass(frozen=True)
class DetectionContext:
    """
    Context information for intent detection
    """
    query: QueryText
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    language: str = "vi"
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Metadata = field(default_factory=dict)
    
    def with_metadata(self, **kwargs) -> 'DetectionContext':
        """Create new context with additional metadata"""
        new_metadata = {**self.metadata, **kwargs}
        return DetectionContext(
            query=self.query,
            user_id=self.user_id,
            session_id=self.session_id,
            language=self.language,
            timestamp=self.timestamp,
            metadata=new_metadata
        )


@dataclass
class DetectionSession:
    """
    Entity representing a detection session for analytics
    """
    session_id: UUID = field(default_factory=uuid4)
    user_id: Optional[str] = None
    start_time: datetime = field(default_factory=datetime.utcnow)
    end_time: Optional[datetime] = None
    queries: List[QueryText] = field(default_factory=list)
    results: List[IntentResult] = field(default_factory=list)
    metadata: Metadata = field(default_factory=dict)
    
    def add_query(self, query: QueryText, result: IntentResult) -> None:
        """Add a query and its result to the session"""
        self.queries.append(query)
        self.results.append(result)
    
    def close_session(self) -> None:
        """Close the session"""
        self.end_time = datetime.utcnow()
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get session duration in seconds"""
        if self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return None
    
    @property
    def query_count(self) -> int:
        """Get number of queries in session"""
        return len(self.queries)
    
    @property
    def avg_confidence(self) -> float:
        """Get average confidence across all results"""
        if not self.results:
            return 0.0
        return sum(r.confidence for r in self.results) / len(self.results)


@dataclass(frozen=True)
class IntentRule:
    """
    Entity representing an intent detection rule
    """
    intent_id: IntentId
    keywords: List[str]
    patterns: List[str]  # Regex patterns as strings
    weight: float = 1.0
    enabled: bool = True
    description: Optional[str] = None
    
    def __post_init__(self):
        if self.weight <= 0:
            raise ValueError(f"Weight must be positive, got {self.weight}")
        if not self.keywords and not self.patterns:
            raise ValueError("Rule must have at least one keyword or pattern")
    
    @property
    def priority(self) -> str:
        """Get rule priority based on weight"""
        if self.weight >= 1.5:
            return "high"
        elif self.weight >= 1.0:
            return "medium"
        else:
            return "low"
