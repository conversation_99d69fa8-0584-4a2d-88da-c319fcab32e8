"""
Domain-specific exceptions for intent detection system
"""

from typing import Optional, Dict, Any


class IntentDetectionError(Exception):
    """Base exception for intent detection errors"""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.metadata = metadata or {}
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for serialization"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "metadata": self.metadata
        }


class InvalidQueryError(IntentDetectionError):
    """Raised when query is invalid or malformed"""
    
    def __init__(self, query: str, reason: str):
        super().__init__(
            f"Invalid query: {reason}",
            error_code="INVALID_QUERY",
            metadata={"query": query, "reason": reason}
        )


class ConfigurationError(IntentDetectionError):
    """Raised when configuration is invalid"""
    
    def __init__(self, config_key: str, reason: str):
        super().__init__(
            f"Configuration error for '{config_key}': {reason}",
            error_code="CONFIGURATION_ERROR",
            metadata={"config_key": config_key, "reason": reason}
        )


class VectorStoreError(IntentDetectionError):
    """Raised when vector store operations fail"""
    
    def __init__(self, operation: str, reason: str):
        super().__init__(
            f"Vector store operation '{operation}' failed: {reason}",
            error_code="VECTOR_STORE_ERROR",
            metadata={"operation": operation, "reason": reason}
        )


class EmbeddingError(IntentDetectionError):
    """Raised when embedding generation fails"""
    
    def __init__(self, text: str, reason: str):
        super().__init__(
            f"Failed to generate embedding: {reason}",
            error_code="EMBEDDING_ERROR",
            metadata={"text_length": len(text), "reason": reason}
        )


class RerankerError(IntentDetectionError):
    """Raised when reranking fails"""
    
    def __init__(self, reason: str, candidate_count: int = 0):
        super().__init__(
            f"Reranking failed: {reason}",
            error_code="RERANKER_ERROR",
            metadata={"reason": reason, "candidate_count": candidate_count}
        )


class CacheError(IntentDetectionError):
    """Raised when cache operations fail"""
    
    def __init__(self, operation: str, key: str, reason: str):
        super().__init__(
            f"Cache operation '{operation}' failed for key '{key}': {reason}",
            error_code="CACHE_ERROR",
            metadata={"operation": operation, "key": key, "reason": reason}
        )


class RuleValidationError(IntentDetectionError):
    """Raised when rule validation fails"""
    
    def __init__(self, rule_id: str, reason: str):
        super().__init__(
            f"Rule validation failed for '{rule_id}': {reason}",
            error_code="RULE_VALIDATION_ERROR",
            metadata={"rule_id": rule_id, "reason": reason}
        )


class ThresholdError(IntentDetectionError):
    """Raised when confidence thresholds are invalid"""
    
    def __init__(self, threshold_name: str, value: float, reason: str):
        super().__init__(
            f"Invalid threshold '{threshold_name}' = {value}: {reason}",
            error_code="THRESHOLD_ERROR",
            metadata={"threshold_name": threshold_name, "value": value, "reason": reason}
        )


class ServiceUnavailableError(IntentDetectionError):
    """Raised when a required service is unavailable"""
    
    def __init__(self, service_name: str, reason: str):
        super().__init__(
            f"Service '{service_name}' is unavailable: {reason}",
            error_code="SERVICE_UNAVAILABLE",
            metadata={"service_name": service_name, "reason": reason}
        )


class TimeoutError(IntentDetectionError):
    """Raised when operations timeout"""
    
    def __init__(self, operation: str, timeout_seconds: float):
        super().__init__(
            f"Operation '{operation}' timed out after {timeout_seconds}s",
            error_code="TIMEOUT_ERROR",
            metadata={"operation": operation, "timeout_seconds": timeout_seconds}
        )


class RateLimitError(IntentDetectionError):
    """Raised when rate limits are exceeded"""
    
    def __init__(self, limit: int, window_seconds: int):
        super().__init__(
            f"Rate limit exceeded: {limit} requests per {window_seconds}s",
            error_code="RATE_LIMIT_ERROR",
            metadata={"limit": limit, "window_seconds": window_seconds}
        )


class ValidationError(IntentDetectionError):
    """Raised when input validation fails"""
    
    def __init__(self, field: str, value: Any, reason: str):
        super().__init__(
            f"Validation failed for field '{field}': {reason}",
            error_code="VALIDATION_ERROR",
            metadata={"field": field, "value": str(value), "reason": reason}
        )


# Exception hierarchy for better error handling
RETRIABLE_ERRORS = (
    VectorStoreError,
    EmbeddingError,
    CacheError,
    ServiceUnavailableError,
    TimeoutError
)

CONFIGURATION_ERRORS = (
    ConfigurationError,
    RuleValidationError,
    ThresholdError,
    ValidationError
)

CLIENT_ERRORS = (
    InvalidQueryError,
    RateLimitError,
    ValidationError
)

SERVER_ERRORS = (
    VectorStoreError,
    EmbeddingError,
    RerankerError,
    CacheError,
    ServiceUnavailableError,
    TimeoutError
)
