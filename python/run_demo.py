#!/usr/bin/env python3
"""
Demo runner script for the refactored intent detection system
"""

import sys
import os
from pathlib import Path

# Add src_refactored to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src_refactored"
sys.path.insert(0, str(src_dir))

# Now import and run the demo
if __name__ == "__main__":
    try:
        from main_demo import run_demo
        import asyncio
        
        print("🌸 Starting FPT University Agent Demo...")
        asyncio.run(run_demo())
        
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
