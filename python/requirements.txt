# Core dependencies for FPT University Agent - Refactored
# Python 3.9+

# Async framework
asyncio-mqtt>=0.11.0

# Structured logging
structlog>=23.1.0

# Type checking and validation
pydantic>=2.0.0
typing-extensions>=4.7.0

# Text processing
unidecode>=1.3.6

# Optional dependencies (install as needed)

# Vector stores
# qdrant-client>=1.6.0  # Uncomment for Qdrant support

# Embeddings
# openai>=1.0.0  # Uncomment for OpenAI embeddings
# sentence-transformers>=2.2.0  # Uncomment for local embeddings
# torch>=2.0.0  # Required for sentence-transformers

# Caching
# redis>=4.5.0  # Uncomment for Redis caching

# Reranking
# transformers>=4.30.0  # Uncomment for CrossEncoder reranking

# Development dependencies (install separately)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# pytest-cov>=4.1.0
# mypy>=1.5.0
# black>=23.7.0
# flake8>=6.0.0
# pre-commit>=3.3.0
