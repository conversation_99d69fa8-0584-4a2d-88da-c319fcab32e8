[project]
name = "fpt-agent-python"
version = "0.1.0"
description = "FPT University Agent System rewritten in Python with Agno framework"
authors = [
    {name = "nghiahoang",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12, <3.14"
dependencies = [
    "agno (>=1.7.1,<2.0.0)",
    "openai (>=1.93.0,<2.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "qdrant-client (>=1.14.3,<2.0.0)",
    "llama-index (>=0.12.46,<0.13.0)",
    "llama-index-embeddings-openai (>=0.3.1,<0.4.0)",
    "llama-index-vector-stores-qdrant (>=0.6.1,<0.7.0)",
    "sentence-transformers (>=3.0.1,<4.0.0)",
    "structlog (>=25.4.0,<26.0.0)",
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
