#!/usr/bin/env python3
"""
Full demo for the refactored intent detection system
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add src_refactored to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src_refactored"
sys.path.insert(0, str(src_dir))

# Import components
from core.domain.entities import DetectionContext, IntentRule, IntentResult
from core.domain.exceptions import IntentDetectionError
from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
from infrastructure.caching.memory_cache import MemoryCacheService
from infrastructure.vector_stores.memory_store import MemoryVectorStore
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl
from shared.types import DetectionMethod, Result

# Sample intent rules for demo
DEMO_RULES = [
    IntentRule(
        intent_id="tuition_inquiry",
        keywords=[
            'học phí', 'tuition', 'phí', 'tiền', 'cost', 'chi phí', 'trả góp',
            'thanh toán', 'ojt', 'fee', 'mắc', 'đắt', 'rẻ', 'expensive', 'cheap'
        ],
        patterns=[
            r"học phí.*(?:bao nhiêu|đắt|rẻ|giá|cost|mắc)",
            r"(?:chi phí|tiền|phí).*(?:học|ngành|năm|semester|kỳ)",
            r"tuition.*(?:fee|của|bao nhiêu|FPT|program|IT|AI)",
        ],
        weight=1.4,
        description="Tuition and fee inquiries"
    ),
    IntentRule(
        intent_id="campus_info",
        keywords=[
            'campus', 'cơ sở', 'thư viện', 'library', 'ký túc xá', 'dormitory',
            'cơ sở vật chất', 'facilities', 'địa chỉ', 'address', 'location'
        ],
        patterns=[
            r"(?:campus|cơ sở).*(?:ở đâu|where|địa chỉ|location)",
            r"thư viện.*(?:mở cửa|giờ|hours|time)",
            r"ký túc xá.*(?:giá|price|cost|bao nhiêu)",
        ],
        weight=1.2,
        description="Campus and facilities information"
    ),
    IntentRule(
        intent_id="program_requirements",
        keywords=[
            'điểm chuẩn', 'admission', 'requirements', 'yêu cầu', 'đầu vào',
            'tuyển sinh', 'enrollment', 'điều kiện', 'conditions'
        ],
        patterns=[
            r"điểm chuẩn.*(?:bao nhiêu|2024|2025|năm)",
            r"(?:yêu cầu|requirements).*(?:đầu vào|admission|tuyển sinh)",
            r"(?:điều kiện|conditions).*(?:học|study|program)",
        ],
        weight=1.3,
        description="Program admission requirements"
    ),
    IntentRule(
        intent_id="program_search",
        keywords=[
            'ngành', 'program', 'major', 'chuyên ngành', 'specialization',
            'cntt', 'it', 'ai', 'artificial intelligence', 'software engineering'
        ],
        patterns=[
            r"ngành.*(?:nào|gì|what|which)",
            r"(?:program|major).*(?:available|có|offer)",
            r"(?:cntt|it|ai).*(?:program|ngành)",
        ],
        weight=1.1,
        description="Program and major search"
    )
]

# Test queries
TEST_QUERIES = [
    "Học phí FPT 2025 bao nhiêu tiền?",
    "Thư viện mở cửa lúc mấy giờ?", 
    "Điểm chuẩn FPT 2024 bao nhiêu?",
    "Campus FPT ở đâu?",
    "Yêu cầu đầu vào ngành IT như thế nào?",
    "Ký túc xá FPT giá bao nhiêu một tháng?",
    "Tuition fee for AI program?",
    "What programs are available?",
    "FPT có những ngành nào?",
    "Hôm nay trời có mưa không?",  # Irrelevant
    "Cách nấu phở bò Hà Nội?"      # Irrelevant
]


class SimpleIntentDetectionService:
    """
    Simplified intent detection service for demo
    """
    
    def __init__(
        self,
        rule_detector: RuleBasedDetectorImpl,
        cache_service: MemoryCacheService,
        text_processor: VietnameseTextProcessor,
        metrics_collector: MetricsCollectorImpl
    ):
        self.rule_detector = rule_detector
        self.cache_service = cache_service
        self.text_processor = text_processor
        self.metrics_collector = metrics_collector
        
        # Thresholds
        self.high_confidence_threshold = 0.7
        self.medium_confidence_threshold = 0.3
    
    async def detect(self, context: DetectionContext) -> Result[IntentResult]:
        """Detect intent using rule-based approach"""
        start_time = time.time()
        query = context.query
        
        self.metrics_collector.increment_counter("intent_detection_requests")
        
        try:
            # Check for irrelevant queries
            if self.text_processor.is_irrelevant_query(query):
                result = IntentResult(
                    id="general_info",
                    confidence=0.1,
                    method=DetectionMethod.FALLBACK,
                    metadata={"reason": "irrelevant_query"}
                )
                processing_time = (time.time() - start_time) * 1000
                result = result.with_metadata(processing_time_ms=processing_time)
                return Result.ok(result)
            
            # Check cache
            cache_key = self._get_cache_key(query)
            cached_result = await self.cache_service.get(cache_key)
            if cached_result:
                self.metrics_collector.increment_counter("cache_hits")
                return Result.ok(IntentResult(**cached_result))
            
            self.metrics_collector.increment_counter("cache_misses")
            
            # Rule-based detection
            rule_match = await self.rule_detector.detect(query)
            
            if rule_match:
                result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight,
                        "match_position": rule_match.position
                    }
                )
            else:
                # Fallback
                result = IntentResult(
                    id="general_info",
                    confidence=0.2,
                    method=DetectionMethod.FALLBACK,
                    metadata={"reason": "no_rule_match"}
                )
            
            processing_time = (time.time() - start_time) * 1000
            result = result.with_metadata(processing_time_ms=processing_time)
            
            # Cache result if confidence is reasonable
            if result.confidence >= self.medium_confidence_threshold:
                cache_data = {
                    "id": result.id,
                    "confidence": result.confidence,
                    "method": result.method,
                    "metadata": result.metadata,
                    "timestamp": result.timestamp.isoformat()
                }
                await self.cache_service.set(cache_key, cache_data, ttl_seconds=300)
            
            self.metrics_collector.increment_counter("intent_detection_success")
            self.metrics_collector.record_histogram("intent_detection_duration", processing_time)
            
            return Result.ok(result)
            
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            self.metrics_collector.increment_counter("intent_detection_errors")
            self.metrics_collector.record_histogram("intent_detection_duration", processing_time)
            return Result.error(str(e), "DETECTION_ERROR")
    
    def _get_cache_key(self, query: str) -> str:
        """Generate cache key"""
        import hashlib
        return f"intent:{hashlib.md5(query.encode()).hexdigest()}"
    
    async def get_performance_metrics(self):
        """Get performance metrics"""
        return self.metrics_collector.get_all_metrics()


async def run_full_demo():
    """Run full intent detection demo"""
    
    print("🌸 FPT University Agent - Full Demo")
    print("=" * 50)
    
    # Initialize components
    print("🔧 Initializing components...")
    
    text_processor = VietnameseTextProcessor()
    metrics_collector = MetricsCollectorImpl()
    cache_service = MemoryCacheService(max_size=1000, default_ttl=300)
    rule_detector = RuleBasedDetectorImpl(
        rules=DEMO_RULES,
        text_processor=text_processor
    )
    
    # Create intent detection service
    intent_service = SimpleIntentDetectionService(
        rule_detector=rule_detector,
        cache_service=cache_service,
        text_processor=text_processor,
        metrics_collector=metrics_collector
    )
    
    print("✅ Components initialized successfully!")
    print()
    
    # Test individual queries
    print("🎯 Testing intent detection...")
    print("-" * 30)
    
    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n{i}. Query: '{query}'")
        
        context = DetectionContext(
            query=query,
            user_id="demo_user",
            session_id="demo_session",
            language="vi" if any(ord(c) > 127 for c in query) else "en"
        )
        
        start_time = time.time()
        result = await intent_service.detect(context)
        duration_ms = (time.time() - start_time) * 1000
        
        if result.is_ok():
            intent_result = result.data
            confidence_icon = "🟢" if intent_result.confidence >= 0.7 else "🟡" if intent_result.confidence >= 0.5 else "🔴"
            
            print(f"   {confidence_icon} Intent: {intent_result.id}")
            print(f"   📊 Confidence: {intent_result.confidence:.3f}")
            print(f"   🔧 Method: {intent_result.method}")
            print(f"   ⏱️ Duration: {duration_ms:.1f}ms")
            
            if intent_result.metadata:
                # Show relevant metadata
                if "matched_keywords" in intent_result.metadata:
                    print(f"   🔑 Keywords: {intent_result.metadata['matched_keywords']}")
                if "matched_patterns" in intent_result.metadata:
                    print(f"   📝 Patterns: {len(intent_result.metadata['matched_patterns'])}")
                if "reason" in intent_result.metadata:
                    print(f"   💭 Reason: {intent_result.metadata['reason']}")
        else:
            print(f"   ❌ Error: {result.error}")
        
        await asyncio.sleep(0.1)  # Small delay for demo effect
    
    print()
    
    # Test cache effectiveness
    print("💾 Testing cache effectiveness...")
    print("Repeating first 3 queries to test cache...")
    
    cache_test_queries = TEST_QUERIES[:3]
    for query in cache_test_queries:
        context = DetectionContext(query=query, user_id="cache_test")
        start_time = time.time()
        result = await intent_service.detect(context)
        duration_ms = (time.time() - start_time) * 1000
        
        if result.is_ok():
            print(f"   Query: '{query[:30]}...' -> {duration_ms:.1f}ms")
    
    print()
    
    # Performance metrics
    print("📊 Performance Metrics:")
    print("-" * 20)
    
    metrics = await intent_service.get_performance_metrics()
    counters = metrics.get('counters', {})
    histograms = metrics.get('histograms', {})
    
    print(f"Total requests: {counters.get('intent_detection_requests', 0)}")
    print(f"Cache hits: {counters.get('cache_hits', 0)}")
    print(f"Cache misses: {counters.get('cache_misses', 0)}")
    print(f"Successful detections: {counters.get('intent_detection_success', 0)}")
    print(f"Errors: {counters.get('intent_detection_errors', 0)}")
    
    if 'intent_detection_duration' in histograms:
        duration_stats = histograms['intent_detection_duration']
        print(f"Average response time: {duration_stats.get('avg', 0):.1f}ms")
        print(f"P95 response time: {duration_stats.get('p95', 0):.1f}ms")
    
    # Cache stats
    cache_stats = await cache_service.get_stats()
    print(f"Cache hit rate: {cache_stats.get('hit_rate', 0):.1f}%")
    print(f"Cache size: {cache_stats.get('size', 0)}")
    
    print()
    
    # Rule analysis
    print("📋 Rule Analysis:")
    print("-" * 15)
    
    rules = await rule_detector.get_rules()
    for rule in rules:
        print(f"  {rule.intent_id}:")
        print(f"    Weight: {rule.weight}")
        print(f"    Keywords: {len(rule.keywords)}")
        print(f"    Patterns: {len(rule.patterns)}")
        print(f"    Priority: {rule.priority}")
        print()
    
    print("🎉 Full demo completed successfully!")
    print("✅ Rule-based intent detection is working properly!")


if __name__ == "__main__":
    try:
        asyncio.run(run_full_demo())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
