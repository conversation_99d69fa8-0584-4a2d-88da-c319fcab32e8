#!/usr/bin/env python3
"""
Performance Analysis cho Hybrid Intent Detection Service
"""
from src.intent_detection import HybridIntentDetectionService
import sys
import os
import time
import statistics
from typing import List, Dict, Any

# Thêm thư mục gốc của project vào Python Path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


# Test queries cho performance
PERFORMANCE_QUERIES = [
    "Học phí FPT 2025 bao nhiêu tiền?",
    "Mỗi kỳ phải đóng bao nhiêu?",
    "Hoc phi nganh an toan thong tin?",
    "SE program tuition?",
    "Thư viện mở cửa lúc mấy giờ?",
    "Có chỗ nào để ngồi học bài yên tĩnh không?",
    "Điểm chuẩn FPT 2024 bao nhiêu?",
    "Tôi muốn học AI thì cần trình độ như thế nào?",
    "FPT có ngành gì hot nhất?",
    "Họ<PERSON> bổng 100% có thật không?",
    "Trường có chính sách hỗ trợ cho sinh viên nghèo không?",
    "Học xong có dễ kiếm việc không?",
    "so sanh fpt vs bkhn",
    "han chot nop ho so?",
    "Hôm nay trời có mưa không?",
    "Cách nấu phở bò Hà Nội?",
    "Giá vàng hôm nay thế nào?"
]


def measure_performance(service: HybridIntentDetectionService, num_runs: int = 5) -> Dict[str, Any]:
    """Đo performance cho tất cả queries"""
    all_times = []
    method_stats = {"rule": [], "rerank": [], "fallback": []}

    print(f"🔥 Đang đo performance với {num_runs} lần chạy cho mỗi query...")
    print("=" * 70)

    for query in PERFORMANCE_QUERIES:
        query_times = []

        for run in range(num_runs):
            start_time = time.time()
            result = service.detect(query)
            end_time = time.time()

            response_time = (end_time - start_time) * \
                1000  # Convert to milliseconds
            query_times.append(response_time)

            # Phân loại theo method
            method = result.get("method", "unknown")
            if method in method_stats:
                method_stats[method].append(response_time)

        avg_time = statistics.mean(query_times)
        all_times.extend(query_times)

        print(f"Query: {query[:50]:<50} | Avg: {avg_time:.2f}ms")

    return {
        "total_queries": len(PERFORMANCE_QUERIES) * num_runs,
        "avg_response_time": statistics.mean(all_times),
        "median_response_time": statistics.median(all_times),
        "min_response_time": min(all_times),
        "max_response_time": max(all_times),
        "std_dev": statistics.stdev(all_times) if len(all_times) > 1 else 0,
        "method_breakdown": {
            method: {
                "count": len(times),
                "avg_time": statistics.mean(times) if times else 0,
                "median_time": statistics.median(times) if times else 0
            }
            for method, times in method_stats.items() if times
        }
    }


def run_performance_analysis():
    """Chạy phân tích performance toàn diện"""
    print("🚀 PERFORMANCE ANALYSIS - HYBRID INTENT DETECTION")
    print("=" * 70)

    # Khởi tạo service
    print("⏳ Đang khởi tạo service...")
    service = HybridIntentDetectionService()
    print("✅ Service đã sẵn sàng!\n")

    # Warmup
    print("🔥 Warming up...")
    for _ in range(3):
        service.detect("Học phí FPT bao nhiêu?")
    print("✅ Warmup hoàn tất!\n")

    # Đo performance
    stats = measure_performance(service, num_runs=3)

    # In kết quả
    print("\n" + "=" * 70)
    print("📊 PERFORMANCE SUMMARY")
    print("=" * 70)
    print(f"Total Queries Processed: {stats['total_queries']}")
    print(f"Average Response Time:   {stats['avg_response_time']:.2f}ms")
    print(f"Median Response Time:    {stats['median_response_time']:.2f}ms")
    print(f"Min Response Time:       {stats['min_response_time']:.2f}ms")
    print(f"Max Response Time:       {stats['max_response_time']:.2f}ms")
    print(f"Standard Deviation:      {stats['std_dev']:.2f}ms")

    print("\n📈 BREAKDOWN BY METHOD:")
    for method, data in stats['method_breakdown'].items():
        print(
            f"  {method.upper():<10} | Count: {data['count']:<3} | Avg: {data['avg_time']:.2f}ms | Median: {data['median_time']:.2f}ms")

    print("\n🎯 PERFORMANCE RATING:")
    avg_time = stats['avg_response_time']
    if avg_time < 100:
        rating = "🟢 EXCELLENT"
    elif avg_time < 300:
        rating = "🟡 GOOD"
    elif avg_time < 500:
        rating = "🟠 ACCEPTABLE"
    else:
        rating = "🔴 NEEDS IMPROVEMENT"

    print(f"  Overall Performance: {rating}")
    print("=" * 70)


if __name__ == "__main__":
    run_performance_analysis()
