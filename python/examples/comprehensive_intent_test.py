#!/usr/bin/env python3
"""
Comprehensive Test Suite for Hybrid Intent Detection Service
"""
from src.intent_detection import HybridIntentDetectionService
import sys
import os

# Thêm thư mục gốc của project vào Python Path
# Điều này cần thiết để có thể import `src` từ thư mục `examples`
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


# --- BỘ TEST CASE TOÀN DIỆN ---

TEST_SUITE = {
    "1. Direct Questions (Rule-Based Check)": [
        {"query": "Học phí FPT 2025 bao nhiêu tiền?",
            "intent": "tuition_inquiry", "method": "rule"},
        {"query": "Thư viện mở cửa lúc mấy giờ?",
            "intent": "campus_info", "method": "rule"},
        {"query": "Điểm chuẩn FPT 2024 bao nhiêu?",
            "intent": "program_requirements", "method": "rule"},
        {"query": "FPT có ngành gì hot nhất?",
            "intent": "program_search", "method": "rule"},
        {"query": "Học bổng 100% có thật không?",
            "intent": "scholarship_inquiry", "method": "rule"},
    ],
    "2. Semantic Questions (Vector Search Check)": [
        {"query": "Mỗi kỳ phải đóng bao nhiêu?",
            "intent": "tuition_inquiry", "method": "vector"},
        {"query": "Có chỗ nào để ngồi học bài yên tĩnh không?",
            "intent": "campus_info", "method": "vector"},
        {"query": "Tôi muốn học AI thì cần trình độ như thế nào?",
            "intent": "program_requirements", "method": "vector"},
        {"query": "Trường có chính sách hỗ trợ cho sinh viên nghèo không?",
            "intent": "scholarship_inquiry", "method": "vector"},
        {"query": "Học xong có dễ kiếm việc không?",
            "intent": "career_guidance", "method": "vector"},
    ],
    "3. Edge Cases": [
        {"query": "học phí it?", "intent": "tuition_inquiry", "desc": "Short query"},
        {"query": "Hoc phi nganh an toan thong tin?",
            "intent": "tuition_inquiry", "desc": "No accents"},
        {"query": "SE program tuition?",
            "intent": "tuition_inquiry", "desc": "Code-mix"},
        {"query": "so sanh fpt vs bkhn", "intent": "comparative_inquiry",
            "desc": "No accents + Acronym"},
        {"query": "han chot nop ho so?",
            "intent": "deadline_inquiry", "desc": "No accents"},
    ],
    "4. Irrelevant & Fallback": [
        {"query": "Hôm nay trời có mưa không?", "intent": "general_info",
            "method": "fallback", "desc": "Weather question"},
        {"query": "Cách nấu phở bò Hà Nội?", "intent": "general_info",
            "method": "fallback", "desc": "Cooking recipe"},
        {"query": "Giá vàng hôm nay thế nào?", "intent": "general_info",
            "method": "fallback", "desc": "Gold price"},
    ]
}


def run_comprehensive_test():
    """Chạy toàn bộ bộ test và in ra báo cáo chi tiết"""
    print("🤖 Running Comprehensive Test Suite for Intent Detection")
    print("=" * 70)

    service = HybridIntentDetectionService()
    overall_stats = {"passed": 0, "failed": 0, "total": 0}

    for category, cases in TEST_SUITE.items():
        print(f"\n--- {category} ---")
        category_stats = {"passed": 0, "failed": 0, "total": len(cases)}
        overall_stats["total"] += len(cases)

        for i, case in enumerate(cases):
            query = case["query"]
            expected_intent = case["intent"]

            print(f"\nTest {i+1}/{len(cases)}: \"{query}\"")
            if "desc" in case:
                print(f"   ({case['desc']})")
            print(f"   Expected: {expected_intent}")

            try:
                result = service.detect(query)
                actual_intent = result.get("id", "N/A")
                method = result.get("method", "N/A")
                confidence = result.get("confidence", 0)

                if actual_intent == expected_intent:
                    status = "✅ PASS"
                    category_stats["passed"] += 1
                    overall_stats["passed"] += 1
                else:
                    status = "❌ FAIL"
                    category_stats["failed"] += 1
                    overall_stats["failed"] += 1

                print(
                    f"   {status} -> Got: {actual_intent} (Confidence: {confidence:.2f}, Method: {method.upper()})")

            except Exception as e:
                status = "💥 ERROR"
                category_stats["failed"] += 1
                overall_stats["failed"] += 1
                print(f"   {status}: {e}")

        print(
            f"--- {category} Summary: {category_stats['passed']}/{category_stats['total']} passed ---")

    print("\n" + "=" * 70)
    print("📊 OVERALL TEST SUMMARY")
    print(f"  Total Cases: {overall_stats['total']}")
    print(f"  ✅ Passed:    {overall_stats['passed']}")
    print(f"  ❌ Failed:    {overall_stats['failed']}")

    pass_rate = (overall_stats['passed'] / overall_stats['total']
                 ) * 100 if overall_stats['total'] > 0 else 0
    print(f"  Pass Rate:   {pass_rate:.2f}%")
    print("=" * 70)


if __name__ == "__main__":
    run_comprehensive_test()
