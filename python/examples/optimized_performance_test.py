#!/usr/bin/env python3
"""
Optimized Performance Test cho Hybrid Intent Detection Service
"""
from src.intent_detection import HybridIntentDetectionService
import sys
import os
import time
import statistics
from typing import List, Dict, Any

# Thêm thư mục gốc của project vào Python Path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


# Test queries với cache testing
PERFORMANCE_QUERIES = [
    "Học phí FPT 2025 bao nhiêu tiền?",
    "Mỗi kỳ phải đóng bao nhiêu?",
    "Hoc phi nganh an toan thong tin?",
    "SE program tuition?",  # Test code-mixing improvement
    "Thư viện mở cửa lúc mấy giờ?",
    "Có chỗ nào để ngồi học bài yên tĩnh không?",
    "Điểm chuẩn FPT 2024 bao nhiêu?",
    "Tôi muốn học AI thì cần trình độ như thế nào?",
    "FPT có ngành gì hot nhất?",
    "Học bổng 100% có thật không?",
    "Trường có chính sách hỗ trợ cho sinh viên nghèo không?",
    "Học xong có dễ kiếm việc không?",
    "so sanh fpt vs bkhn",
    "han chot nop ho so?",
    "Hôm nay trời có mưa không?",
    "Cách nấu phở bò Hà Nội?",
    "Giá vàng hôm nay thế nào?",
    # Thêm queries để test cache
    "Học phí FPT 2025 bao nhiêu tiền?",  # Duplicate để test cache
    "Mỗi kỳ phải đóng bao nhiêu?",  # Duplicate để test cache
]


def test_cache_performance(service: HybridIntentDetectionService):
    """Test hiệu suất cache"""
    print("🔥 Testing Cache Performance...")

    # Test query đầu tiên (cache miss)
    query = "Học phí FPT 2025 bao nhiêu tiền?"

    start_time = time.time()
    result1 = service.detect(query)
    first_call_time = (time.time() - start_time) * 1000

    # Test query thứ hai (cache hit)
    start_time = time.time()
    result2 = service.detect(query)
    second_call_time = (time.time() - start_time) * 1000

    print(f"   First call (cache miss): {first_call_time:.2f}ms")
    print(f"   Second call (cache hit): {second_call_time:.2f}ms")
    print(f"   Cache speedup: {first_call_time/second_call_time:.1f}x")

    # Verify results are identical
    assert result1 == result2, "Cache results should be identical"
    print("   ✅ Cache results verified identical")


def test_accuracy_improvements(service: HybridIntentDetectionService):
    """Test cải thiện accuracy"""
    print("\n🎯 Testing Accuracy Improvements...")

    test_cases = [
        {"query": "SE program tuition?", "expected": "tuition_inquiry"},
        {"query": "Hoc phi nganh an toan thong tin?", "expected": "tuition_inquiry"},
        {"query": "han chot nop ho so?", "expected": "deadline_inquiry"},
        {"query": "so sanh fpt vs bkhn", "expected": "comparative_inquiry"},
    ]

    correct = 0
    for case in test_cases:
        result = service.detect(case["query"])
        actual = result.get("id")
        expected = case["expected"]

        if actual == expected:
            status = "✅ PASS"
            correct += 1
        else:
            status = "❌ FAIL"

        print(f"   {case['query'][:40]:<40} | {status} | Got: {actual}")

    accuracy = (correct / len(test_cases)) * 100
    print(f"   Accuracy: {accuracy:.1f}% ({correct}/{len(test_cases)})")

    return accuracy


def run_optimized_performance_test():
    """Chạy test performance với optimization"""
    print("🚀 OPTIMIZED PERFORMANCE TEST")
    print("=" * 70)

    # Khởi tạo service
    print("⏳ Initializing optimized service...")
    service = HybridIntentDetectionService()
    print("✅ Service ready!\n")

    # Test cache performance
    test_cache_performance(service)

    # Test accuracy improvements
    accuracy = test_accuracy_improvements(service)

    # Warmup
    print("\n🔥 Warming up...")
    for _ in range(3):
        service.detect("Học phí FPT bao nhiêu?")
    print("✅ Warmup complete!")

    # Reset stats để đo chính xác
    service.reset_performance_stats()

    # Đo performance
    print(
        f"\n🔥 Running performance test with {len(PERFORMANCE_QUERIES)} queries...")
    print("=" * 70)

    all_times = []
    start_total = time.time()

    for i, query in enumerate(PERFORMANCE_QUERIES):
        start_time = time.time()
        result = service.detect(query)
        end_time = time.time()

        response_time = (end_time - start_time) * 1000
        all_times.append(response_time)

        if i < 5:  # Chỉ hiển thị 5 queries đầu
            print(
                f"Query {i+1}: {query[:40]:<40} | {response_time:.2f}ms | {result.get('method', 'N/A').upper()}")

    total_time = (time.time() - start_total) * 1000

    # Lấy performance stats
    stats = service.get_performance_stats()

    print("\n" + "=" * 70)
    print("📊 OPTIMIZED PERFORMANCE SUMMARY")
    print("=" * 70)
    print(f"Total Queries:           {len(PERFORMANCE_QUERIES)}")
    print(f"Total Time:              {total_time:.2f}ms")
    print(f"Average Response Time:   {statistics.mean(all_times):.2f}ms")
    print(f"Median Response Time:    {statistics.median(all_times):.2f}ms")
    print(f"Min Response Time:       {min(all_times):.2f}ms")
    print(f"Max Response Time:       {max(all_times):.2f}ms")
    print(f"Standard Deviation:      {statistics.stdev(all_times):.2f}ms")

    print(f"\n📈 OPTIMIZATION METRICS:")
    print(f"Rule Calls:              {stats['rule_calls']}")
    print(f"Vector Calls:            {stats['vector_calls']}")
    print(f"Cache Hits:              {stats['cache_hits']}")
    print(f"Cache Hit Rate:          {stats['cache_hit_rate']:.1f}%")
    print(f"Cache Size:              {stats['cache_size']} entries")

    print(f"\n🎯 OVERALL ASSESSMENT:")
    avg_time = statistics.mean(all_times)
    if avg_time < 50:
        rating = "🟢 EXCELLENT"
    elif avg_time < 150:
        rating = "🟡 GOOD"
    elif avg_time < 300:
        rating = "🟠 ACCEPTABLE"
    else:
        rating = "🔴 NEEDS IMPROVEMENT"

    print(f"Performance Rating:      {rating}")
    print(f"Accuracy Score:          {accuracy:.1f}%")

    # Tính overall score
    perf_score = max(0, 100 - avg_time/5)  # 100 điểm nếu < 5ms
    overall_score = (perf_score * 0.6 + accuracy * 0.4)
    print(f"Overall Score:           {overall_score:.1f}/100")

    print("=" * 70)

    return {
        "avg_response_time": avg_time,
        "accuracy": accuracy,
        "overall_score": overall_score,
        "cache_hit_rate": stats['cache_hit_rate']
    }


if __name__ == "__main__":
    results = run_optimized_performance_test()

    print(f"\n🎉 OPTIMIZATION RESULTS:")
    print(f"   Average Response Time: {results['avg_response_time']:.2f}ms")
    print(f"   Accuracy: {results['accuracy']:.1f}%")
    print(f"   Cache Hit Rate: {results['cache_hit_rate']:.1f}%")
    print(f"   Overall Score: {results['overall_score']:.1f}/100")
