#!/usr/bin/env python3
"""
FPT University Agent - Refactored Demo
Clean Architecture Intent Detection System
"""


import sys
import asyncio
import time
from pathlib import Path
from typing import List

# Add src to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Import components
from core.domain.entities import DetectionContext, IntentRule, IntentResult
from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
from infrastructure.intent_detection.rule_loader import ProductionRuleLoader, get_default_demo_rules
from infrastructure.caching.memory_cache import MemoryCacheService
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl
from shared.types import DetectionMethod, Result

def load_rules(use_production: bool = True) -> List[IntentRule]:
    """Load rules - production or demo fallback"""
    print(f"📋 Loading rules (production: {use_production})...")

    if use_production:
        try:
            loader = ProductionRuleLoader()
            rules = loader.load_rules()

            # Show rule coverage
            metadata = loader.get_rules_metadata()
            if metadata:
                coverage_areas = metadata.get('coverage_areas', [])
                if coverage_areas:
                    print(f"📊 Coverage areas: {coverage_areas}")

            return rules
        except Exception as e:
            print(f"⚠️ Failed to load production rules: {e}")
            print("🔄 Falling back to demo rules...")
            return get_default_demo_rules()
    else:
        return get_default_demo_rules()

TEST_QUERIES = [
    # Tuition inquiries
    "Học phí FPT 2025 bao nhiêu tiền?",
    "Chi phí học ngành CNTT như thế nào?",
    "Tuition fee for AI program?",
    "Có học bổng không?",

    # Admission requirements
    "Điểm chuẩn FPT 2024 bao nhiêu?",
    "Yêu cầu đầu vào ngành IT như thế nào?",
    "Hồ sơ đăng ký cần gì?",

    # Program information
    "FPT có những ngành nào?",
    "What programs are available?",
    "Ngành AI có học không?",

    # Campus facilities
    "Campus FPT ở đâu?",
    "Thư viện mở cửa lúc mấy giờ?",
    "Ký túc xá giá bao nhiêu?",

    # Student services
    "Dịch vụ sinh viên có gì?",
    "Hỗ trợ tìm việc làm không?",

    # Technical support
    "Portal FAP bị lỗi làm sao?",
    "Quên mật khẩu email trường?",

    # Out of scope
    "Hôm nay trời có mưa không?",
    "Cách nấu phở bò Hà Nội?"
]


class SimpleIntentDetectionService:
    """Simplified intent detection service for demo"""
    
    def __init__(self, rule_detector, cache_service, text_processor, metrics_collector):
        self.rule_detector = rule_detector
        self.cache_service = cache_service
        self.text_processor = text_processor
        self.metrics_collector = metrics_collector
    
    async def detect(self, context: DetectionContext) -> Result[IntentResult]:
        """Detect intent using rule-based approach"""
        start_time = time.time()
        query = context.query
        
        self.metrics_collector.increment_counter("intent_detection_requests")
        
        try:
            # Check for irrelevant queries
            if self.text_processor.is_irrelevant_query(query):
                result = IntentResult(
                    id="general_info",
                    confidence=0.1,
                    method=DetectionMethod.FALLBACK,
                    metadata={"reason": "irrelevant_query"}
                )
                processing_time = (time.time() - start_time) * 1000
                result = result.with_metadata(processing_time_ms=processing_time)
                return Result.ok(result)
            
            # Check cache
            cache_key = self._get_cache_key(query)
            cached_result = await self.cache_service.get(cache_key)
            if cached_result:
                self.metrics_collector.increment_counter("cache_hits")
                return Result.ok(IntentResult(**cached_result))
            
            self.metrics_collector.increment_counter("cache_misses")
            
            # Rule-based detection
            rule_match = await self.rule_detector.detect(query)
            
            if rule_match:
                result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight
                    }
                )
            else:
                result = IntentResult(
                    id="general_info",
                    confidence=0.2,
                    method=DetectionMethod.FALLBACK,
                    metadata={"reason": "no_rule_match"}
                )
            
            processing_time = (time.time() - start_time) * 1000
            result = result.with_metadata(processing_time_ms=processing_time)
            
            # Cache result
            if result.confidence >= 0.3:
                cache_data = {
                    "id": result.id,
                    "confidence": result.confidence,
                    "method": result.method,
                    "metadata": result.metadata,
                    "timestamp": result.timestamp.isoformat()
                }
                await self.cache_service.set(cache_key, cache_data, ttl_seconds=300)
            
            self.metrics_collector.increment_counter("intent_detection_success")
            return Result.ok(result)
            
        except Exception as e:
            self.metrics_collector.increment_counter("intent_detection_errors")
            return Result.error(str(e), "DETECTION_ERROR")
    
    def _get_cache_key(self, query: str) -> str:
        import hashlib
        return f"intent:{hashlib.md5(query.encode()).hexdigest()}"
    
    async def get_performance_metrics(self):
        return self.metrics_collector.get_all_metrics()


async def run_demo(mode="full"):
    """Run intent detection demo"""
    
    print("🌸 FPT University Agent - Refactored Demo")
    print("=" * 50)
    
    if mode == "simple":
        print("Running simple functionality test...")
        return await run_simple_test()
    
    # Initialize components
    print("🔧 Initializing components...")
    
    text_processor = VietnameseTextProcessor()
    metrics_collector = MetricsCollectorImpl()
    cache_service = MemoryCacheService(max_size=1000, default_ttl=300)
    # Load production rules
    rules = load_rules(use_production=True)
    rule_detector = RuleBasedDetectorImpl(rules=rules, text_processor=text_processor)
    
    intent_service = SimpleIntentDetectionService(
        rule_detector=rule_detector,
        cache_service=cache_service,
        text_processor=text_processor,
        metrics_collector=metrics_collector
    )
    
    print("✅ Components initialized successfully!")
    print()
    
    # Test queries
    print("🎯 Testing intent detection...")
    print("-" * 30)
    
    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n{i}. Query: '{query}'")
        
        context = DetectionContext(
            query=query,
            user_id="demo_user",
            language="vi" if any(ord(c) > 127 for c in query) else "en"
        )
        
        start_time = time.time()
        result = await intent_service.detect(context)
        duration_ms = (time.time() - start_time) * 1000
        
        if result.is_ok():
            intent_result = result.data
            confidence_icon = "🟢" if intent_result.confidence >= 0.7 else "🟡" if intent_result.confidence >= 0.5 else "🔴"
            
            print(f"   {confidence_icon} Intent: {intent_result.id}")
            print(f"   📊 Confidence: {intent_result.confidence:.3f}")
            print(f"   🔧 Method: {intent_result.method}")
            print(f"   ⏱️ Duration: {duration_ms:.1f}ms")
            
            if "matched_keywords" in intent_result.metadata:
                print(f"   🔑 Keywords: {intent_result.metadata['matched_keywords']}")
        else:
            print(f"   ❌ Error: {result.error}")
        
        await asyncio.sleep(0.05)
    
    # Performance metrics
    print("\n📊 Performance Summary:")
    print("-" * 20)
    
    metrics = await intent_service.get_performance_metrics()
    counters = metrics.get('counters', {})
    
    print(f"Total requests: {counters.get('intent_detection_requests', 0)}")
    print(f"Cache hits: {counters.get('cache_hits', 0)}")
    print(f"Cache misses: {counters.get('cache_misses', 0)}")
    print(f"Successful detections: {counters.get('intent_detection_success', 0)}")
    print(f"Errors: {counters.get('intent_detection_errors', 0)}")
    
    cache_stats = await cache_service.get_stats()
    print(f"Cache hit rate: {cache_stats.get('hit_rate', 0):.1f}%")
    
    print("\n🎉 Demo completed successfully!")
    print("✅ Clean Architecture intent detection is working!")


async def run_simple_test():
    """Run simple functionality test"""
    
    try:
        # Test imports
        from core.domain.entities import IntentResult, DetectionContext
        from shared.utils.text_processing import VietnameseTextProcessor
        print("✅ Core imports successful")
        
        # Test text processor
        processor = VietnameseTextProcessor()
        normalized = processor.normalize_vietnamese("Học phí FPT")
        print(f"✅ Text processing: '{normalized}'")
        
        # Test entities
        result = IntentResult(
            id="test_intent",
            confidence=0.95,
            method=DetectionMethod.RULE
        )
        print(f"✅ Entity creation: {result.id} ({result.confidence})")
        
        print("\n🎉 Simple test passed! System is ready.")
        return True
        
    except Exception as e:
        print(f"❌ Simple test failed: {e}")
        return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="FPT University Agent Demo")
    parser.add_argument(
        "--mode", 
        choices=["simple", "full"], 
        default="full",
        help="Demo mode: simple (basic test) or full (complete demo)"
    )
    
    args = parser.parse_args()
    
    try:
        asyncio.run(run_demo(args.mode))
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
