#!/usr/bin/env python3
"""
Simple demo to test basic functionality
"""

import sys
import os
import asyncio
import time
from pathlib import Path

# Add src_refactored to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src_refactored"
sys.path.insert(0, str(src_dir))

# Test basic imports
def test_imports():
    """Test if we can import basic components"""
    print("🔧 Testing imports...")
    
    try:
        # Test core domain
        from core.domain.entities import IntentResult, DetectionContext, IntentRule
        from core.domain.value_objects import QueryAnalysis, ConfidenceScore
        from shared.types import DetectionMethod, ConfidenceLevel
        print("✅ Core domain imports successful")
        
        # Test shared utilities
        from shared.utils.text_processing import VietnameseTextProcessor
        from shared.utils.metrics import MetricsCollectorImpl
        print("✅ Shared utilities imports successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_text_processor():
    """Test Vietnamese text processor"""
    print("\n📝 Testing Vietnamese text processor...")
    
    try:
        from shared.utils.text_processing import VietnameseTextProcessor
        
        processor = VietnameseTextProcessor()
        
        # Test normalization
        test_queries = [
            "Hoc phi FPT bao nhieu?",
            "học phí fpt university",
            "tuition fee for IT program"
        ]
        
        for query in test_queries:
            normalized = processor.normalize_vietnamese(query)
            keywords = processor.extract_keywords(query)
            language = processor.detect_language(query)
            
            print(f"  Query: '{query}'")
            print(f"    Normalized: '{normalized}'")
            print(f"    Keywords: {keywords}")
            print(f"    Language: {language}")
            print()
        
        print("✅ Text processor test successful")
        return True
        
    except Exception as e:
        print(f"❌ Text processor test failed: {e}")
        return False


def test_entities():
    """Test domain entities"""
    print("🎯 Testing domain entities...")
    
    try:
        from core.domain.entities import IntentResult, DetectionContext, IntentRule
        from shared.types import DetectionMethod, IntentCategory
        from datetime import datetime
        
        # Test IntentResult
        result = IntentResult(
            id="tuition_inquiry",
            confidence=0.85,
            method=DetectionMethod.RULE,
            category=IntentCategory.TUITION_INQUIRY,
            metadata={"test": "data"}
        )
        
        print(f"  Intent Result: {result.id}")
        print(f"  Confidence Level: {result.confidence_level}")
        print(f"  Is High Confidence: {result.is_high_confidence()}")
        
        # Test DetectionContext
        context = DetectionContext(
            query="Học phí FPT bao nhiêu?",
            user_id="test_user",
            language="vi"
        )
        
        print(f"  Context Query: {context.query}")
        print(f"  Context Language: {context.language}")
        
        # Test IntentRule
        rule = IntentRule(
            intent_id="tuition_inquiry",
            keywords=["học phí", "tuition"],
            patterns=["học phí.*bao nhiêu"],
            weight=1.4
        )
        
        print(f"  Rule Intent: {rule.intent_id}")
        print(f"  Rule Priority: {rule.priority}")
        
        print("✅ Domain entities test successful")
        return True
        
    except Exception as e:
        print(f"❌ Domain entities test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_value_objects():
    """Test value objects"""
    print("💎 Testing value objects...")
    
    try:
        from core.domain.value_objects import QueryAnalysis, ConfidenceScore
        
        # Test QueryAnalysis
        analysis = QueryAnalysis.analyze("Học phí FPT University bao nhiêu tiền?")
        
        print(f"  Original: {analysis.original_query}")
        print(f"  Normalized: {analysis.normalized_query}")
        print(f"  Language: {analysis.language}")
        print(f"  Word Count: {analysis.word_count}")
        print(f"  Contains Vietnamese: {analysis.contains_vietnamese}")
        print(f"  Keywords: {analysis.detected_keywords}")
        
        # Test ConfidenceScore
        score = ConfidenceScore.from_rule_match(0.8, ["học phí", "FPT"])
        
        print(f"  Confidence Value: {score.value}")
        print(f"  Confidence Source: {score.source}")
        print(f"  Confidence Reasoning: {score.reasoning}")
        
        print("✅ Value objects test successful")
        return True
        
    except Exception as e:
        print(f"❌ Value objects test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_memory_cache():
    """Test memory cache"""
    print("💾 Testing memory cache...")
    
    try:
        from infrastructure.caching.memory_cache import MemoryCacheService
        
        cache = MemoryCacheService(max_size=100, default_ttl=60)
        
        # Test set and get
        await cache.set("test_key", {"result": "test_value"}, ttl_seconds=30)
        
        result = await cache.get("test_key")
        print(f"  Cached result: {result}")
        
        # Test stats
        stats = await cache.get_stats()
        print(f"  Cache stats: {stats}")
        
        print("✅ Memory cache test successful")
        return True
        
    except Exception as e:
        print(f"❌ Memory cache test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_memory_vector_store():
    """Test memory vector store"""
    print("🗄️ Testing memory vector store...")
    
    try:
        from infrastructure.vector_stores.memory_store import MemoryVectorStore
        from core.domain.entities import SearchCandidate
        
        store = MemoryVectorStore()
        
        # Add some test documents
        texts = [
            "Học phí ngành CNTT là 25 triệu đồng",
            "Thư viện mở cửa từ 7h đến 22h",
            "Điểm chuẩn FPT 2024 là 22 điểm"
        ]
        
        vectors = [
            [0.1, 0.2, 0.3] * 512,  # Dummy 1536D vector
            [0.2, 0.3, 0.4] * 512,
            [0.3, 0.4, 0.5] * 512
        ]
        
        metadata = [
            {"intent_id": "tuition_inquiry", "program": "IT"},
            {"intent_id": "campus_info", "facility": "library"},
            {"intent_id": "program_requirements", "year": "2024"}
        ]
        
        await store.add_documents(texts, vectors, metadata)
        
        # Test search
        query_vector = [0.15, 0.25, 0.35] * 512
        candidates = await store.search(query_vector, top_k=2)
        
        print(f"  Added {len(texts)} documents")
        print(f"  Search returned {len(candidates)} candidates")
        
        for i, candidate in enumerate(candidates):
            print(f"    {i+1}. {candidate.intent_id} (score: {candidate.score:.3f})")
            print(f"       Text: {candidate.text[:50]}...")
        
        # Test collection info
        info = await store.get_collection_info()
        print(f"  Collection info: {info}")
        
        print("✅ Memory vector store test successful")
        return True
        
    except Exception as e:
        print(f"❌ Memory vector store test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_simple_demo():
    """Run simple demo"""
    print("🌸 FPT University Agent - Simple Demo")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        return
    
    # Test text processor
    if not test_text_processor():
        return
    
    # Test entities
    if not test_entities():
        return
    
    # Test value objects
    if not test_value_objects():
        return
    
    # Test async components
    if not await test_memory_cache():
        return
    
    if not await test_memory_vector_store():
        return
    
    print("\n🎉 All tests passed! Basic functionality is working.")
    print("✅ Ready to proceed with full demo implementation.")


if __name__ == "__main__":
    try:
        asyncio.run(run_simple_demo())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
